const mongoose = require("mongoose");
require("dotenv").config();

// Import existing models
const Complaint = require("./models/Complaint");
const User = require("./models/User");

async function createTestComplaints() {
  try {
    // Connect to database
    await mongoose.connect(process.env.DB_URL, {
      dbName: process.env.DB_NAME || "ecommerce",
    });
    console.log("✅ Connected to MongoDB");

    // Check existing complaints
    const existingComplaints = await Complaint.find({});
    console.log(`📊 Found ${existingComplaints.length} existing complaints`);

    if (existingComplaints.length > 0) {
      console.log(
        "✅ Complaints already exist. Displaying existing complaints:"
      );
      const populatedComplaints = await Complaint.find({})
        .populate("createdBy", "name email")
        .limit(5);

      populatedComplaints.forEach((complaint, index) => {
        console.log(
          `   ${index + 1}. Complaint ${complaint._id} - Status: ${
            complaint.status
          } - User: ${complaint.createdBy?.name}`
        );
      });
      return;
    }

    // Get existing users
    const users = await User.find({});

    if (users.length === 0) {
      console.log("❌ No users found. Cannot create complaints without users.");
      return;
    }

    console.log(`👥 Found ${users.length} users`);

    // Create test complaints
    const testComplaints = [
      {
        title: "Product quality issue",
        description:
          "The Aloe Vera juice had an unusual taste and smell. Upon opening the bottle, I noticed the liquid was cloudy and had a strange odor. This is concerning as I have purchased this product before and it was always clear and odorless.",
        priority: "high",
        status: "open",
        createdBy: users[0]._id,
        createdAt: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000), // 2 days ago
      },
      {
        title: "Great product experience",
        description:
          "Very satisfied with the Ashwagandha capsules. They have helped improve my energy levels and overall well-being. The quality is excellent and delivery was prompt.",
        priority: "low",
        status: "resolved",
        createdBy: users[1] ? users[1]._id : users[0]._id,
        createdAt: new Date(Date.now() - 3 * 24 * 60 * 60 * 1000), // 3 days ago
      },
      {
        title: "Delivery delay",
        description:
          "Order was delivered 3 days late without any prior notification. This caused inconvenience as I needed the medicine urgently. Please improve your delivery tracking system.",
        priority: "medium",
        status: "in_progress",
        createdBy: users[2] ? users[2]._id : users[0]._id,
        createdAt: new Date(Date.now() - 4 * 24 * 60 * 60 * 1000), // 4 days ago
      },
      {
        title: "Website navigation issue",
        description:
          "Difficulty finding product categories on the website. The menu structure is confusing and it takes too long to locate specific products. Consider improving the user interface.",
        priority: "medium",
        status: "open",
        createdBy: users[3] ? users[3]._id : users[0]._id,
        createdAt: new Date(Date.now() - 1 * 24 * 60 * 60 * 1000), // 1 day ago
      },
      {
        title: "Excellent customer service",
        description:
          "The customer support team was very helpful in resolving my query about product usage. Very satisfied with the service quality and quick response time.",
        priority: "low",
        status: "resolved",
        createdBy: users[4] ? users[4]._id : users[0]._id,
        createdAt: new Date(Date.now() - 5 * 24 * 60 * 60 * 1000), // 5 days ago
      },
      {
        title: "Packaging issue",
        description:
          "The product packaging was damaged during delivery. The bottle was cracked and some content was spilled. Please improve packaging to prevent damage during transit.",
        priority: "high",
        status: "in_progress",
        createdBy: users[0]._id,
        createdAt: new Date(Date.now() - 6 * 24 * 60 * 60 * 1000), // 6 days ago
      },
    ];

    // Insert complaints
    const createdComplaints = await Complaint.insertMany(testComplaints);
    console.log(
      `✅ Created ${createdComplaints.length} test complaints successfully!`
    );

    // Display created complaints
    const populatedComplaints = await Complaint.find({}).populate(
      "createdBy",
      "name email"
    );

    populatedComplaints.forEach((complaint, index) => {
      console.log(
        `   ${index + 1}. Complaint ${complaint._id} - Status: ${
          complaint.status
        } - User: ${complaint.createdBy?.name} - Title: ${complaint.title}`
      );
    });

    console.log(
      "\n💡 Test complaints created! You can now test the Complaints & Feedback page."
    );
  } catch (error) {
    console.error("❌ Error:", error.message);
  } finally {
    await mongoose.disconnect();
    console.log("🔌 Disconnected from MongoDB");
  }
}

createTestComplaints();
