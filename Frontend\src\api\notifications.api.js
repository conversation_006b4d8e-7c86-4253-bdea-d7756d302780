// Notifications API Service
import serviceWorkerAPI from "./serviceworker.api.js";

class NotificationsAPI {
  // Get all notifications with optional filters
  async getNotifications(filters = {}) {
    try {
      const response = await serviceWorkerAPI.getNotifications(filters);
      console.log("Notifications fetched successfully:", response);
      return response;
    } catch (error) {
      console.error("Failed to fetch notifications:", error);
      throw error;
    }
  }

  // Mark notification as read
  async markNotificationRead(id) {
    try {
      const response = await serviceWorkerAPI.markNotificationRead(id);
      console.log("Notification marked as read:", response);
      return response;
    } catch (error) {
      console.error("Failed to mark notification as read:", error);
      throw error;
    }
  }

  // Mark all notifications as read
  async markAllNotificationsRead() {
    try {
      const response = await serviceWorkerAPI.markAllNotificationsRead();
      console.log("All notifications marked as read:", response);
      return response;
    } catch (error) {
      console.error("Failed to mark all notifications as read:", error);
      throw error;
    }
  }

  // Get unread notification count
  async getUnreadNotificationCount() {
    try {
      const response = await serviceWorkerAPI.getUnreadNotificationCount();
      console.log("Unread notification count fetched:", response);
      return response;
    } catch (error) {
      console.error("Failed to fetch unread notification count:", error);
      throw error;
    }
  }

  // Format notification for display
  formatNotificationForDisplay(notification) {
    try {
      return {
        id: notification._id || notification.id,
        type: notification.type || 'info',
        title: notification.title || 'Notification',
        message: notification.message || notification.content || '',
        time: this.formatTime(notification.createdAt || notification.timestamp),
        icon: this.getNotificationIcon(notification.type),
        priority: notification.priority || 'medium',
        read: notification.read || notification.isRead || false,
        category: notification.category || 'general',
        data: notification.data || {}
      };
    } catch (error) {
      console.error('Error formatting notification:', error);
      return {
        id: notification._id || notification.id || Math.random().toString(36).substr(2, 9),
        type: 'info',
        title: 'Error Loading Notification',
        message: 'There was an error loading this notification',
        time: 'Unknown',
        icon: 'Bell',
        priority: 'low',
        read: false,
        category: 'error',
        data: {}
      };
    }
  }

  // Get icon name based on notification type
  getNotificationIcon(type) {
    const iconMap = {
      'alert': 'AlertCircle',
      'warning': 'AlertTriangle',
      'success': 'CheckCircle',
      'info': 'Bell',
      'report': 'BarChart3',
      'update': 'FileText',
      'calendar': 'Calendar',
      'order': 'Package',
      'user': 'User',
      'system': 'Settings',
      'error': 'XCircle'
    };
    return iconMap[type] || 'Bell';
  }

  // Format time for display
  formatTime(timestamp) {
    try {
      if (!timestamp) return 'Unknown time';
      
      const date = new Date(timestamp);
      const now = new Date();
      const diffInMinutes = Math.floor((now - date) / (1000 * 60));
      
      if (diffInMinutes < 1) return 'Just now';
      if (diffInMinutes < 60) return `${diffInMinutes} minute${diffInMinutes > 1 ? 's' : ''} ago`;
      
      const diffInHours = Math.floor(diffInMinutes / 60);
      if (diffInHours < 24) return `${diffInHours} hour${diffInHours > 1 ? 's' : ''} ago`;
      
      const diffInDays = Math.floor(diffInHours / 24);
      if (diffInDays < 7) return `${diffInDays} day${diffInDays > 1 ? 's' : ''} ago`;
      
      return date.toLocaleDateString();
    } catch (error) {
      console.error('Error formatting time:', error);
      return 'Unknown time';
    }
  }

  // Get notification categories with counts
  getNotificationCategories(notifications) {
    try {
      const categories = {
        all: notifications.length,
        alerts: notifications.filter(n => n.type === 'alert' || n.type === 'warning').length,
        reports: notifications.filter(n => n.type === 'report').length,
        updates: notifications.filter(n => n.type === 'update' || n.type === 'info').length,
        unread: notifications.filter(n => !n.read).length
      };

      return [
        { title: "All", count: categories.all, active: true, filter: 'all' },
        { title: "Alerts", count: categories.alerts, active: false, filter: 'alerts' },
        { title: "Reports", count: categories.reports, active: false, filter: 'reports' },
        { title: "Updates", count: categories.updates, active: false, filter: 'updates' },
        { title: "Unread", count: categories.unread, active: false, filter: 'unread' }
      ];
    } catch (error) {
      console.error('Error getting notification categories:', error);
      return [
        { title: "All", count: 0, active: true, filter: 'all' },
        { title: "Alerts", count: 0, active: false, filter: 'alerts' },
        { title: "Reports", count: 0, active: false, filter: 'reports' },
        { title: "Updates", count: 0, active: false, filter: 'updates' },
        { title: "Unread", count: 0, active: false, filter: 'unread' }
      ];
    }
  }

  // Filter notifications by category
  filterNotificationsByCategory(notifications, category) {
    try {
      switch (category) {
        case 'alerts':
          return notifications.filter(n => n.type === 'alert' || n.type === 'warning');
        case 'reports':
          return notifications.filter(n => n.type === 'report');
        case 'updates':
          return notifications.filter(n => n.type === 'update' || n.type === 'info');
        case 'unread':
          return notifications.filter(n => !n.read);
        case 'all':
        default:
          return notifications;
      }
    } catch (error) {
      console.error('Error filtering notifications:', error);
      return [];
    }
  }
}

const notificationsAPI = new NotificationsAPI();
export default notificationsAPI;
