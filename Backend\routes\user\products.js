const express = require('express');
const router = express.Router();
const {
    getProducts,
    getProductById,
    getFeaturedProducts,
    addProductReview,
    updateProductReview,
    deleteProductReview
} = require('../../controllers/productController');
const { protect } = require('../../middleware/auth');

// Public routes
// @route   GET /api/products
// @desc    Get all products with filtering
// @access  Public
router.get('/', getProducts);

// @route   GET /api/products/featured
// @desc    Get featured products
// @access  Public
router.get('/featured', getFeaturedProducts);

// @route   GET /api/products/:id
// @desc    Get product by ID
// @access  Public
router.get('/:id', getProductById);

// Private routes (require authentication)
router.use(protect);

// @route   POST /api/products/:id/reviews
// @desc    Add product review
// @access  Private
router.post('/:id/reviews', addProductReview);

// @route   PUT /api/products/:id/reviews/:reviewId
// @desc    Update product review
// @access  Private
router.put('/:id/reviews/:reviewId', updateProductReview);

// @route   DELETE /api/products/:id/reviews/:reviewId
// @desc    Delete product review
// @access  Private
router.delete('/:id/reviews/:reviewId', deleteProductReview);

// @route   GET /api/user/products/stats
// @desc    Get product statistics (user)
// @access  Private/User
router.get('/stats', require('../../controllers/productController').getProductStats);

module.exports = router;
