const mongoose = require("mongoose");
const bcrypt = require("bcryptjs");
require("dotenv").config();

// Import User model
const User = require("./models/User");

async function createTestAdmin() {
  try {
    // Connect to database
    await mongoose.connect(process.env.DB_URL, {
      dbName: process.env.DB_NAME || "ecommerce",
    });
    console.log("✅ Connected to MongoDB");

    // List all existing users first
    const allUsers = await User.find({}, "name email role status phone");
    console.log("\n📋 Existing users in database:");
    allUsers.forEach((user) => {
      console.log(
        `  - ${user.name} (${user.email}) - Role: ${user.role}, Status: ${user.status}, Phone: ${user.phone}`
      );
    });

    // Check if admin already exists
    const existingAdmin = await User.findOne({ email: "<EMAIL>" });
    if (existingAdmin) {
      console.log("\n✅ Admin user already exists:", existingAdmin.email);
      console.log("📧 Email:", existingAdmin.email);
      console.log("🔑 Role:", existingAdmin.role);
      console.log("📊 Status:", existingAdmin.status);
      console.log("✉️ Email Verified:", existingAdmin.isEmailVerified);
      console.log("📱 Phone:", existingAdmin.phone);

      // Check if employee exists too
      const existingEmployee = await User.findOne({
        email: "<EMAIL>",
      });
      if (existingEmployee) {
        console.log(
          "\n✅ Employee user already exists:",
          existingEmployee.email
        );
        console.log("📧 Email:", existingEmployee.email);
        console.log("🔑 Role:", existingEmployee.role);
        console.log("📊 Status:", existingEmployee.status);
        console.log("✉️ Email Verified:", existingEmployee.isEmailVerified);
        console.log("📱 Phone:", existingEmployee.phone);
      }
      return;
    }

    // Hash password
    const salt = await bcrypt.genSalt(10);
    const hashedPassword = await bcrypt.hash("Admin123!", salt);

    // Create admin user
    const adminUser = new User({
      name: "Admin User",
      email: "<EMAIL>",
      password: hashedPassword,
      phone: "9876543210",
      role: "admin",
      department: "Administration",
      status: "active",
      isEmailVerified: true,
      paymentMethod: "card",
      joinedDate: Date.now(),
    });

    await adminUser.save();
    console.log("✅ Test admin user created successfully!");
    console.log("📧 Email: <EMAIL>");
    console.log("🔑 Password: Admin123!");
    console.log("👤 Role: admin");

    // Also create a test employee
    const existingEmployee = await User.findOne({
      email: "<EMAIL>",
    });
    if (!existingEmployee) {
      const employeePassword = await bcrypt.hash("Employee123!", salt);
      const employeeUser = new User({
        name: "Employee User",
        email: "<EMAIL>",
        password: employeePassword,
        phone: "8765432109",
        role: "employee",
        department: "Sales",
        status: "active",
        isEmailVerified: true,
        paymentMethod: "card",
        joinedDate: Date.now(),
      });

      await employeeUser.save();
      console.log("✅ Test employee user created successfully!");
      console.log("📧 Email: <EMAIL>");
      console.log("🔑 Password: Employee123!");
      console.log("👤 Role: employee");
    } else {
      console.log("✅ Employee user already exists:", existingEmployee.email);
    }
  } catch (error) {
    console.error("❌ Error creating test users:", error);
  } finally {
    await mongoose.disconnect();
    console.log("🔌 Disconnected from MongoDB");
  }
}

// Run the function
createTestAdmin();
