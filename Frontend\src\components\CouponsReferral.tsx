import { useState, useEffect } from "react";
import { <PERSON>, <PERSON><PERSON>ontent, Card<PERSON><PERSON>er, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Input } from "@/components/ui/input";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from "@/components/ui/tabs";
import {
  Plus,
  Edit,
  Trash2,
  Copy,
  Sparkles,
  Eye,
  RefreshCw,
  Lock,
} from "lucide-react";
import { useToast } from "@/hooks/use-toast";
import CreateCouponModal from "./CreateCouponModal";
import CreateDiscountModal from "./CreateDiscountModal";
import CouponViewModal from "./CouponViewModal";
import DiscountViewModal from "./DiscountViewModal";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import AnimatedNumber from "./AnimatedNumber";
import AnimatedText from "./AnimatedText";
import { usePagination } from "@/hooks/use-pagination";
import { TablePagination } from "@/components/ui/table-pagination";
import couponsAPI from "@/api/coupons.api.js";
import { apiHelpers } from "@/utils/apiHelpers";
import useRolePermissions from "@/hooks/useRolePermissions";

const CouponsReferral = () => {
  const { toast } = useToast();
  const permissions = useRolePermissions();

  // State management
  const [coupons, setCoupons] = useState([]);
  const [couponStats, setCouponStats] = useState([
    { title: "Total Coupons", value: "0", color: "text-gray-900" },
    { title: "Active", value: "0", color: "text-green-600" },
    { title: "Inactive", value: "0", color: "text-red-600" },
    { title: "Total Redemption", value: "₹0", color: "text-blue-600" },
  ]);
  const [loading, setLoading] = useState(true);
  const [isRefreshing, setIsRefreshing] = useState(false);
  const [searchTerm, setSearchTerm] = useState("");
  const [statusFilter, setStatusFilter] = useState("all");

  // Modal states
  const [selectedCoupon, setSelectedCoupon] = useState(null);
  const [isCouponModalOpen, setIsCouponModalOpen] = useState(false);
  const [isCreateCouponOpen, setIsCreateCouponOpen] = useState(false);
  const [isViewModalOpen, setIsViewModalOpen] = useState(false);

  // Fetch coupons from API
  const fetchCoupons = async (showLoading = true) => {
    try {
      if (showLoading) setLoading(true);

      const filters = {
        search: searchTerm,
        status: statusFilter !== "all" ? statusFilter : "",
        limit: 100, // Get all coupons for now
      };

      const response = await couponsAPI.getCoupons(filters);

      if (apiHelpers.isSuccessResponse(response)) {
        const data = apiHelpers.extractData(response);
        const couponsData = data.coupons || [];

        // Format coupons for display
        const formattedCoupons = couponsData.map((coupon) =>
          couponsAPI.formatCouponForDisplay(coupon)
        );

        setCoupons(formattedCoupons);

        // Calculate and update statistics
        const stats = couponsAPI.calculateCouponStats(formattedCoupons);
        setCouponStats(stats);

        console.log("Coupons data loaded:", formattedCoupons);
      } else {
        throw new Error(response.message || "Failed to fetch coupons");
      }
    } catch (error) {
      console.error("Failed to fetch coupons:", error);
      toast({
        title: "Error",
        description: apiHelpers.formatError(error),
        variant: "destructive",
      });

      // Set empty state
      setCoupons([]);
      setCouponStats([
        { title: "Total Coupons", value: "0", color: "text-gray-900" },
        { title: "Active", value: "0", color: "text-green-600" },
        { title: "Inactive", value: "0", color: "text-red-600" },
        { title: "Total Redemption", value: "₹0", color: "text-blue-600" },
      ]);
    } finally {
      setLoading(false);
      setIsRefreshing(false);
    }
  };

  // Load coupons on component mount and when filters change
  useEffect(() => {
    fetchCoupons();
  }, [searchTerm, statusFilter]);

  // Refresh coupons
  const handleRefresh = async () => {
    setIsRefreshing(true);
    await fetchCoupons(false);
  };

  // Filter coupons based on search and status
  const filteredCoupons = coupons.filter((coupon) => {
    const matchesSearch =
      coupon.code.toLowerCase().includes(searchTerm.toLowerCase()) ||
      coupon.description.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesStatus =
      statusFilter === "all" ||
      coupon.status.toLowerCase() === statusFilter.toLowerCase();
    return matchesSearch && matchesStatus;
  });

  // Handle coupon actions
  const handleCreateCoupon = () => {
    setSelectedCoupon(null);
    setIsCreateCouponOpen(true);
  };

  const handleEditCoupon = (coupon) => {
    setSelectedCoupon(coupon);
    setIsCreateCouponOpen(true);
  };

  const handleDeleteCoupon = async (couponId) => {
    try {
      // In a real implementation, you would call the delete API
      // await couponsAPI.deleteCoupon(couponId);

      setCoupons((prev) => prev.filter((c) => c.id !== couponId));
      toast({
        title: "Coupon Deleted",
        description: "Coupon has been successfully deleted",
      });
    } catch (error) {
      console.error("Failed to delete coupon:", error);
      toast({
        title: "Error",
        description: "Failed to delete coupon",
        variant: "destructive",
      });
    }
  };

  const handleViewCoupon = (coupon) => {
    setSelectedCoupon(coupon);
    setIsViewModalOpen(true);
  };

  const handleCopyCouponCode = (code) => {
    navigator.clipboard.writeText(code);
    toast({
      title: "Copied!",
      description: `Coupon code "${code}" copied to clipboard`,
    });
  };

  const getStatusColor = (status) => {
    return couponsAPI.getStatusColor(status);
  };

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-gray-900 dark:text-white">
            Coupons & Referrals
          </h1>
          <p className="text-gray-600 dark:text-gray-300">
            Manage discount coupons and referral programs
          </p>
        </div>
        <div className="flex items-center gap-3">
          <Button
            variant="outline"
            onClick={handleRefresh}
            disabled={isRefreshing}
            className="transition-colors duration-300 ease-in-out"
          >
            <RefreshCw
              className={`w-4 h-4 mr-2 ${isRefreshing ? "animate-spin" : ""}`}
            />
            Refresh
          </Button>
          {permissions.coupons.canAdd ? (
            <Button
              onClick={handleCreateCoupon}
              className="bg-green-600 hover:bg-green-700 text-white"
            >
              <Plus className="w-4 h-4 mr-2" />
              Create Coupon
            </Button>
          ) : (
            <Button
              className="bg-gray-400 text-white cursor-not-allowed"
              disabled
              title="Only administrators can create coupons"
            >
              <Lock className="w-4 h-4 mr-2" />
              Create Coupon (Admin Only)
            </Button>
          )}
        </div>
      </div>

      {/* Statistics Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        {couponStats.map((stat, index) => (
          <Card
            key={index}
            className="bg-white dark:bg-gray-800 border-gray-200 dark:border-gray-700"
          >
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600 dark:text-gray-400">
                    {stat.title}
                  </p>
                  <p className={`text-2xl font-bold ${stat.color}`}>
                    <AnimatedNumber value={stat.value} />
                  </p>
                </div>
                <Sparkles className="w-8 h-8 text-gray-400" />
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      {/* Filters */}
      <Card className="bg-white dark:bg-gray-800 border-gray-200 dark:border-gray-700">
        <CardContent className="p-6">
          <div className="flex flex-col sm:flex-row gap-4">
            <div className="flex-1">
              <Input
                placeholder="Search coupons..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="w-full"
              />
            </div>
            <div className="w-full sm:w-48">
              <select
                value={statusFilter}
                onChange={(e) => setStatusFilter(e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
              >
                <option value="all">All Status</option>
                <option value="active">Active</option>
                <option value="inactive">Inactive</option>
                <option value="expired">Expired</option>
              </select>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Coupons Table */}
      <Card className="bg-white dark:bg-gray-800 border-gray-200 dark:border-gray-700">
        <CardHeader>
          <CardTitle className="text-gray-900 dark:text-white">
            Coupons ({filteredCoupons.length})
          </CardTitle>
        </CardHeader>
        <CardContent>
          {loading ? (
            <div className="space-y-4">
              {[...Array(5)].map((_, index) => (
                <div
                  key={index}
                  className="animate-pulse flex items-center space-x-4 p-4 border rounded-lg"
                >
                  <div className="w-20 h-4 bg-gray-300 dark:bg-gray-600 rounded"></div>
                  <div className="flex-1 space-y-2">
                    <div className="h-4 bg-gray-300 dark:bg-gray-600 rounded w-3/4"></div>
                    <div className="h-3 bg-gray-300 dark:bg-gray-600 rounded w-1/2"></div>
                  </div>
                  <div className="w-16 h-6 bg-gray-300 dark:bg-gray-600 rounded"></div>
                </div>
              ))}
            </div>
          ) : filteredCoupons.length === 0 ? (
            <div className="text-center py-12">
              <Sparkles className="w-16 h-16 text-gray-400 dark:text-gray-500 mx-auto mb-4" />
              <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-2">
                No coupons found
              </h3>
              <p className="text-gray-500 dark:text-gray-400 mb-4">
                {searchTerm || statusFilter !== "all"
                  ? "No coupons match your current filters."
                  : "Get started by creating your first coupon."}
              </p>
              {!searchTerm &&
                statusFilter === "all" &&
                permissions.coupons.canAdd && (
                  <Button
                    onClick={handleCreateCoupon}
                    className="bg-green-600 hover:bg-green-700 text-white"
                  >
                    <Plus className="w-4 h-4 mr-2" />
                    Create First Coupon
                  </Button>
                )}
              {!searchTerm &&
                statusFilter === "all" &&
                !permissions.coupons.canAdd && (
                  <p className="text-gray-400 text-sm mt-2">
                    Contact your administrator to create coupons
                  </p>
                )}
            </div>
          ) : (
            <div className="overflow-x-auto">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Code</TableHead>
                    <TableHead>Description</TableHead>
                    <TableHead>Type</TableHead>
                    <TableHead>Value</TableHead>
                    <TableHead>Usage</TableHead>
                    <TableHead>Status</TableHead>
                    <TableHead>Valid Until</TableHead>
                    <TableHead className="text-right">Actions</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {filteredCoupons.map((coupon) => (
                    <TableRow key={coupon.id}>
                      <TableCell>
                        <div className="flex items-center gap-2">
                          <code className="px-2 py-1 bg-gray-100 dark:bg-gray-700 rounded text-sm font-mono">
                            {coupon.code}
                          </code>
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => handleCopyCouponCode(coupon.code)}
                            className="h-6 w-6 p-0"
                          >
                            <Copy className="w-3 h-3" />
                          </Button>
                        </div>
                      </TableCell>
                      <TableCell>
                        <div>
                          <p className="font-medium text-gray-900 dark:text-white">
                            {coupon.description}
                          </p>
                          <p className="text-sm text-gray-500 dark:text-gray-400">
                            Min Order: ₹{coupon.minOrder}
                          </p>
                        </div>
                      </TableCell>
                      <TableCell>
                        <Badge variant="outline">{coupon.type}</Badge>
                      </TableCell>
                      <TableCell>
                        <span className="font-medium">
                          {coupon.type === "Percentage"
                            ? `${coupon.value}%`
                            : `₹${coupon.value}`}
                        </span>
                        {coupon.maxDiscount > 0 && (
                          <p className="text-xs text-gray-500">
                            Max: ₹{coupon.maxDiscount}
                          </p>
                        )}
                      </TableCell>
                      <TableCell>
                        <div className="text-sm">
                          <span className="font-medium">{coupon.used}</span>
                          <span className="text-gray-500">
                            /{coupon.usageLimit}
                          </span>
                          <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-1.5 mt-1">
                            <div
                              className="bg-blue-600 h-1.5 rounded-full"
                              style={{
                                width: `${Math.min(
                                  (coupon.used / coupon.usageLimit) * 100,
                                  100
                                )}%`,
                              }}
                            ></div>
                          </div>
                        </div>
                      </TableCell>
                      <TableCell>
                        <Badge className={getStatusColor(coupon.status)}>
                          {coupon.status}
                        </Badge>
                      </TableCell>
                      <TableCell>
                        <span className="text-sm text-gray-600 dark:text-gray-400">
                          {coupon.validTo}
                        </span>
                      </TableCell>
                      <TableCell className="text-right">
                        <div className="flex items-center justify-end gap-2">
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => handleViewCoupon(coupon)}
                            title="View Coupon Details"
                          >
                            <Eye className="w-4 h-4" />
                          </Button>
                          {permissions.coupons.canEdit ? (
                            <Button
                              variant="ghost"
                              size="sm"
                              onClick={() => handleEditCoupon(coupon)}
                              title="Edit Coupon"
                            >
                              <Edit className="w-4 h-4" />
                            </Button>
                          ) : (
                            <Button
                              variant="ghost"
                              size="sm"
                              disabled
                              className="opacity-50 cursor-not-allowed"
                              title="Only administrators can edit coupons"
                            >
                              <Lock className="w-4 h-4" />
                            </Button>
                          )}
                          {permissions.coupons.canDelete ? (
                            <Button
                              variant="ghost"
                              size="sm"
                              onClick={() => handleDeleteCoupon(coupon.id)}
                              className="text-red-600 hover:text-red-700"
                              title="Delete Coupon"
                            >
                              <Trash2 className="w-4 h-4" />
                            </Button>
                          ) : (
                            <Button
                              variant="ghost"
                              size="sm"
                              disabled
                              className="opacity-50 cursor-not-allowed"
                              title="Only administrators can delete coupons"
                            >
                              <Lock className="w-4 h-4" />
                            </Button>
                          )}
                        </div>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Modals */}
      <CreateCouponModal
        isOpen={isCreateCouponOpen}
        onClose={() => {
          setIsCreateCouponOpen(false);
          setSelectedCoupon(null);
        }}
        onSave={() => {
          setIsCreateCouponOpen(false);
          setSelectedCoupon(null);
          fetchCoupons(); // Refresh the list
        }}
        coupon={selectedCoupon}
      />

      <CouponViewModal
        isOpen={isViewModalOpen}
        onClose={() => {
          setIsViewModalOpen(false);
          setSelectedCoupon(null);
        }}
        coupon={selectedCoupon}
      />
    </div>
  );
};

export default CouponsReferral;
