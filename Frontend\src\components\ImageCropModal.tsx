import React, { useState, useRef, useCallback, useEffect } from "react";
import ReactCrop, {
  Crop,
  PixelCrop,
  centerCrop,
  makeAspectCrop,
} from "react-image-crop";
import { Dialog, DialogContent, DialogTitle } from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { X, Upload, Camera, AlertCircle } from "lucide-react";
import { useToast } from "@/hooks/use-toast";
import "react-image-crop/dist/ReactCrop.css";

// Custom styles for the slider
const sliderStyles = `
  .slider::-webkit-slider-thumb {
    appearance: none;
    width: 24px;
    height: 24px;
    border-radius: 50%;
    background: #16a34a;
    cursor: pointer;
    border: 2px solid white;
    box-shadow: 0 2px 6px rgba(0,0,0,0.2);
  }
  .slider::-moz-range-thumb {
    width: 24px;
    height: 24px;
    border-radius: 50%;
    background: #16a34a;
    cursor: pointer;
    border: 2px solid white;
    box-shadow: 0 2px 6px rgba(0,0,0,0.2);
  }
`;

interface ImageCropModalProps {
  isOpen: boolean;
  onClose: () => void;
  onCropComplete: (croppedImageUrl: string) => void;
}

const ImageCropModal = ({
  isOpen,
  onClose,
  onCropComplete,
}: ImageCropModalProps) => {
  const { toast } = useToast();
  const [imgSrc, setImgSrc] = useState<string>("");
  const [crop, setCrop] = useState<Crop>();
  const [completedCrop, setCompletedCrop] = useState<PixelCrop>();
  const [scale, setScale] = useState(1);
  const [rotate, setRotate] = useState(0);
  const [isProcessing, setIsProcessing] = useState(false);
  const [error, setError] = useState<string>("");
  const [isUploading, setIsUploading] = useState(false);
  const imgRef = useRef<HTMLImageElement>(null);

  // Constants for validation
  const MAX_FILE_SIZE = 5 * 1024 * 1024; // 5MB
  const ALLOWED_TYPES = [
    "image/jpeg",
    "image/jpg",
    "image/png",
    "image/gif",
    "image/webp",
  ];

  // Clear error after 5 seconds
  useEffect(() => {
    if (error) {
      const timer = setTimeout(() => {
        setError("");
      }, 5000);
      return () => clearTimeout(timer);
    }
  }, [error]);

  const onSelectFile = (e: React.ChangeEvent<HTMLInputElement>) => {
    // Clear any previous errors
    setError("");

    if (e.target.files && e.target.files.length > 0) {
      const file = e.target.files[0];
      setIsUploading(true);

      try {
        // Validate file type
        if (!ALLOWED_TYPES.includes(file.type)) {
          const errorMsg =
            "❌ Unsupported file type. Please upload JPEG, PNG, GIF, or WebP images.";
          setError(errorMsg);
          toast({
            title: "Unsupported File Type",
            description: "Please upload JPEG, PNG, GIF, or WebP images.",
            variant: "destructive",
          });
          setIsUploading(false);
          return;
        }

        // Validate file size
        if (file.size > MAX_FILE_SIZE) {
          const errorMsg =
            "⚠️ File too large. Please upload an image under 5MB.";
          setError(errorMsg);
          toast({
            title: "File Too Large",
            description: "Please upload an image under 5MB.",
            variant: "destructive",
          });
          setIsUploading(false);
          return;
        }

        // Validate file integrity
        if (file.size === 0) {
          const errorMsg = "❌ Invalid file. Please select a valid image.";
          setError(errorMsg);
          toast({
            title: "Invalid File",
            description: "The selected file appears to be corrupted or empty.",
            variant: "destructive",
          });
          setIsUploading(false);
          return;
        }

        setCrop(undefined); // Makes crop preview update between images.
        const reader = new FileReader();

        reader.onload = () => {
          setImgSrc(reader.result?.toString() || "");
          setIsUploading(false);
          toast({
            title: "✅ Image Loaded Successfully",
            description: "You can now crop your profile photo.",
            variant: "default",
          });
        };

        reader.onerror = () => {
          const errorMsg = "❌ Failed to load image. Please try again.";
          setError(errorMsg);
          toast({
            title: "Upload Failed",
            description:
              "Failed to load the image. Please try a different file.",
            variant: "destructive",
          });
          setIsUploading(false);
        };

        reader.readAsDataURL(file);
      } catch (error) {
        const errorMsg = "❌ Unexpected error occurred. Please try again.";
        setError(errorMsg);
        toast({
          title: "Upload Error",
          description:
            "An unexpected error occurred while uploading the image.",
          variant: "destructive",
        });
        setIsUploading(false);
      }
    }

    // Reset the input value to allow selecting the same file again
    e.target.value = "";
  };

  const onImageLoad = (e: React.SyntheticEvent<HTMLImageElement>) => {
    const { width, height } = e.currentTarget;

    // Create a centered crop with 1:1 aspect ratio
    const crop = centerCrop(
      makeAspectCrop(
        {
          unit: "%",
          width: 90,
        },
        1, // aspect ratio 1:1 for profile photos
        width,
        height
      ),
      width,
      height
    );

    setCrop(crop);
  };

  const getCroppedImg = useCallback(
    async (image: HTMLImageElement, crop: PixelCrop, scale = 1, rotate = 0) => {
      const canvas = document.createElement("canvas");
      const ctx = canvas.getContext("2d");

      if (!ctx) {
        throw new Error("No 2d context");
      }

      const scaleX = image.naturalWidth / image.width;
      const scaleY = image.naturalHeight / image.height;
      const pixelRatio = window.devicePixelRatio;

      canvas.width = Math.floor(crop.width * scaleX * pixelRatio);
      canvas.height = Math.floor(crop.height * scaleY * pixelRatio);

      ctx.scale(pixelRatio, pixelRatio);
      ctx.imageSmoothingQuality = "high";

      const cropX = crop.x * scaleX;
      const cropY = crop.y * scaleY;

      const rotateRads = rotate * (Math.PI / 180);
      const centerX = image.naturalWidth / 2;
      const centerY = image.naturalHeight / 2;

      ctx.save();

      // 5) Move the crop origin to the canvas origin (0,0)
      ctx.translate(-cropX, -cropY);
      // 4) Move the origin to the center of the original position
      ctx.translate(centerX, centerY);
      // 3) Rotate around the origin
      ctx.rotate(rotateRads);
      // 2) Scale the image
      ctx.scale(scale, scale);
      // 1) Move the center of the image to the origin (0,0)
      ctx.translate(-centerX, -centerY);
      ctx.drawImage(image, 0, 0);
      ctx.restore();

      return new Promise<string>((resolve) => {
        canvas.toBlob(
          (blob) => {
            if (!blob) {
              throw new Error("Failed to create blob");
            }
            const croppedImageUrl = URL.createObjectURL(blob);
            resolve(croppedImageUrl);
          },
          "image/jpeg",
          0.9
        );
      });
    },
    []
  );

  const handleCropAndUpload = useCallback(async () => {
    if (completedCrop?.width && completedCrop?.height && imgRef.current) {
      setIsProcessing(true);
      try {
        const croppedImageUrl = await getCroppedImg(
          imgRef.current,
          completedCrop,
          scale,
          rotate
        );

        // Convert to base64 for storage
        const response = await fetch(croppedImageUrl);
        const blob = await response.blob();
        const reader = new FileReader();
        reader.onload = () => {
          const base64String = reader.result as string;
          onCropComplete(base64String);
          handleClose();
          toast({
            title: "Profile Photo Updated",
            description:
              "Your profile photo has been successfully updated and will appear across all sections.",
          });
          setIsProcessing(false);
        };
        reader.onerror = () => {
          setError("❌ Failed to process the cropped image. Please try again.");
          toast({
            title: "Upload Failed",
            description:
              "Failed to process the cropped image. Please try again.",
            variant: "destructive",
          });
          setIsProcessing(false);
        };
        reader.readAsDataURL(blob);

        // Clean up the blob URL
        URL.revokeObjectURL(croppedImageUrl);
      } catch (error) {
        console.error("Error cropping image:", error);
        const errorMsg = "❌ Failed to crop image. Please try again.";
        setError(errorMsg);
        toast({
          title: "Crop Failed",
          description:
            error instanceof Error
              ? error.message
              : "Failed to crop image. Please try again.",
          variant: "destructive",
        });
        setIsProcessing(false);
      }
    }
  }, [completedCrop, scale, rotate, getCroppedImg, onCropComplete, toast]);

  const handleClose = () => {
    setImgSrc("");
    setCrop(undefined);
    setCompletedCrop(undefined);
    setScale(1);
    setRotate(0);
    setIsProcessing(false);
    setError("");
    setIsUploading(false);
    onClose();
  };

  return (
    <Dialog open={isOpen} onOpenChange={handleClose}>
      <DialogContent className="sm:max-w-2xl max-h-[90vh] overflow-y-auto">
        <div className="space-y-6">
          {/* Modal Header */}
          <div className="flex items-center justify-center">
            <DialogTitle className="text-2xl font-bold text-gray-900 dark:text-white">
              Crop Profile Photo
            </DialogTitle>
          </div>

          {/* Error Notification */}
          {error && (
            <div className="p-4 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg flex items-center gap-3">
              <AlertCircle className="w-5 h-5 text-red-500 dark:text-red-400 flex-shrink-0" />
              <p className="text-sm font-medium text-red-800 dark:text-red-200 flex-1">
                {error}
              </p>
              <Button
                variant="ghost"
                size="sm"
                onClick={() => setError("")}
                className="text-red-500 hover:text-red-700 dark:text-red-400 dark:hover:text-red-300 p-1"
              >
                <X className="w-4 h-4" />
              </Button>
            </div>
          )}

          {/* Loading Indicator */}
          {isUploading && (
            <div className="p-4 bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg flex items-center gap-3">
              <div className="w-5 h-5 border-2 border-blue-500 border-t-transparent rounded-full animate-spin flex-shrink-0"></div>
              <p className="text-sm font-medium text-blue-800 dark:text-blue-200">
                Uploading image...
              </p>
            </div>
          )}

          {/* Upload Section */}
          {!imgSrc && (
            <div className="text-center space-y-6">
              <div
                className="border-2 border-dashed border-gray-300 dark:border-gray-600 rounded-lg p-12 bg-gray-50 dark:bg-gray-800 hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors cursor-pointer"
                onClick={() =>
                  document.getElementById("crop-file-input")?.click()
                }
              >
                <Upload className="w-16 h-16 mx-auto text-gray-400 dark:text-gray-500 mb-4" />
                <p className="text-xl font-semibold text-gray-900 dark:text-white mb-2">
                  Upload Your Profile Photo
                </p>
                <p className="text-gray-600 dark:text-gray-400 mb-6">
                  Choose a photo for your profile. Max file size: 5MB
                </p>
                <Button
                  onClick={() =>
                    document.getElementById("crop-file-input")?.click()
                  }
                  className="bg-blue-600 hover:bg-blue-700 text-white px-6 py-2"
                >
                  <Upload className="w-4 h-4 mr-2" />
                  Choose Photo
                </Button>
              </div>
              <input
                id="crop-file-input"
                type="file"
                accept="image/*"
                onChange={onSelectFile}
                className="hidden"
              />
            </div>
          )}

          {/* Crop Section */}
          {imgSrc && (
            <div className="space-y-6">
              {/* Instructions */}
              <div className="text-center space-y-2">
                <p className="text-gray-700 dark:text-gray-300">
                  📸 Drag to reposition • 🔍 Use slider to zoom • ✨ Click "Save
                  Photo" to apply
                </p>
              </div>

              {/* Crop Area */}
              <div className="flex justify-center">
                <div className="relative w-full max-w-md h-80 border border-gray-200 dark:border-gray-700 rounded-lg overflow-hidden bg-gray-50 dark:bg-gray-800">
                  <ReactCrop
                    crop={crop}
                    onChange={(_, percentCrop) => setCrop(percentCrop)}
                    onComplete={(c) => setCompletedCrop(c)}
                    aspect={1}
                    minWidth={100}
                    minHeight={100}
                    circularCrop
                    className="w-full h-full"
                  >
                    <img
                      ref={imgRef}
                      alt="Crop me"
                      src={imgSrc}
                      style={{
                        transform: `scale(${scale}) rotate(${rotate}deg)`,
                      }}
                      onLoad={onImageLoad}
                      className="block w-full h-full object-contain"
                    />
                  </ReactCrop>
                </div>
              </div>

              {/* Zoom Controls */}
              <div className="flex items-center justify-center gap-4">
                <label className="text-sm font-medium text-gray-700 dark:text-gray-300">
                  Zoom:
                </label>
                <input
                  type="range"
                  min="0.5"
                  max="3"
                  step="0.1"
                  value={scale}
                  onChange={(e) => setScale(Number(e.target.value))}
                  className="flex-1 max-w-xs h-2 bg-gray-200 dark:bg-gray-700 rounded-lg appearance-none cursor-pointer"
                />
                <span className="text-sm font-medium text-gray-700 dark:text-gray-300 min-w-[3rem]">
                  {Math.round(scale * 100)}%
                </span>
              </div>

              {/* Action Buttons */}
              <div className="flex justify-center gap-4 pt-6">
                <Button
                  variant="outline"
                  onClick={() =>
                    document.getElementById("crop-file-input")?.click()
                  }
                  className="px-6 py-2"
                >
                  <Upload className="w-4 h-4 mr-2" />
                  Choose Different Image
                </Button>
                <Button
                  onClick={handleCropAndUpload}
                  disabled={
                    !completedCrop?.width ||
                    !completedCrop?.height ||
                    isProcessing
                  }
                  className="bg-green-600 hover:bg-green-700 disabled:bg-gray-400 text-white px-6 py-2"
                >
                  {isProcessing ? (
                    <>
                      <div className="w-4 h-4 mr-2 border-2 border-white border-t-transparent rounded-full animate-spin" />
                      Processing...
                    </>
                  ) : (
                    <>
                      <Camera className="w-4 h-4 mr-2" />
                      Save Photo
                    </>
                  )}
                </Button>
              </div>
            </div>
          )}
        </div>
      </DialogContent>
    </Dialog>
  );
};

export default ImageCropModal;
