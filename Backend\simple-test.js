const http = require("http");

const makeRequest = (path) => {
  return new Promise((resolve, reject) => {
    const options = {
      hostname: 'localhost',
      port: 5000,
      path: path,
      method: 'GET'
    };

    const req = http.request(options, (res) => {
      let data = '';
      res.on('data', (chunk) => {
        data += chunk;
      });
      res.on('end', () => {
        try {
          resolve({
            status: res.statusCode,
            data: JSON.parse(data)
          });
        } catch (e) {
          resolve({
            status: res.statusCode,
            data: data
          });
        }
      });
    });

    req.on('error', (err) => {
      reject(err);
    });

    req.end();
  });
};

const testAPI = async () => {
  try {
    console.log("🧪 Testing API Response Structure...\n");

    const response = await makeRequest("/api/admin/products");
    console.log(`Status: ${response.status}`);
    console.log("Response structure:");
    console.log(JSON.stringify(response.data, null, 2));
    
  } catch (error) {
    console.error("Error:", error.message);
  }
};

testAPI();
