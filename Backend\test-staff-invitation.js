require("dotenv").config();
const axios = require("axios");

// Test staff invitation endpoint
async function testStaffInvitation() {
  try {
    console.log("🧪 Testing Staff Invitation Endpoint...\n");

    // First, let's test admin login to get a token
    console.log("1. Testing admin login...");
    const loginResponse = await axios.post(
      "http://localhost:5000/api/auth/login-admin",
      {
        email: "<EMAIL>",
        password: "Admin123!",
      }
    );

    console.log("Login response:", loginResponse.data);

    if (!loginResponse.data.success) {
      console.error("❌ Admin login failed");
      return;
    }

    let token;

    // If login requires 2FA, we need to handle that
    if (loginResponse.data.requiresTwoFactor) {
      console.log("⚠️ Two-factor authentication required");

      // Get the 2FA code from database (for testing purposes)
      const mongoose = require("mongoose");
      const User = require("./models/User");

      require("dotenv").config();
      await mongoose.connect(process.env.DB_URL + process.env.DB_NAME);
      const user = await User.findById(loginResponse.data.userId);
      const twoFactorCode = user.twoFactorCode;
      await mongoose.disconnect();

      console.log("🔐 Using 2FA code:", twoFactorCode);

      // Verify 2FA code
      const verifyResponse = await axios.post(
        "http://localhost:5000/api/auth/verify-code",
        {
          userId: loginResponse.data.userId,
          code: twoFactorCode,
        }
      );

      console.log("2FA verification response:", verifyResponse.data);

      if (!verifyResponse.data.success) {
        console.error("❌ 2FA verification failed");
        return;
      }

      token = verifyResponse.data.token;
      console.log("✅ 2FA verification successful, token obtained");
    } else {
      token = loginResponse.data.token;
    }
    console.log("✅ Admin login successful, token obtained");

    // Now test staff creation
    console.log("\n2. Testing staff invitation...");
    const staffData = {
      name: "Test Staff Member",
      email: "teststaff" + Date.now() + "@example.com",
      phone: "555" + Date.now().toString().slice(-7),
      role: "employee",
      department: "IT",
      position: "Developer",
      status: "active",
    };

    const staffResponse = await axios.post(
      "http://localhost:5000/api/admin/staff",
      staffData,
      {
        headers: {
          Authorization: `Bearer ${token}`,
          "Content-Type": "application/json",
        },
      }
    );

    console.log("Staff creation response:", staffResponse.data);

    if (staffResponse.data.success) {
      console.log("✅ Staff invitation successful!");
      console.log(
        "📧 Invitation email should have been sent to:",
        staffData.email
      );
    } else {
      console.log("❌ Staff invitation failed:", staffResponse.data.message);
    }
  } catch (error) {
    console.error("❌ Test failed:", error.response?.data || error.message);

    if (error.response) {
      console.error("Status:", error.response.status);
      console.error("Headers:", error.response.headers);
    }
  }
}

// Run the test
testStaffInvitation();
