const https = require("https");
const http = require("http");

const BASE_URL = "http://localhost:5000";

async function testAuthFlow() {
  console.log("🧪 Testing Authentication Flow...\n");

  try {
    // Test 1: Register Admin User
    console.log("1. Testing Admin Registration...");
    const registerResponse = await axios.post(`${BASE_URL}/api/auth/register`, {
      name: "Admin User",
      email: "<EMAIL>",
      password: "Admin123!",
      phone: "1234567890",
      role: "admin",
      department: "Administration",
      paymentMethod: "card",
    });
    console.log("✅ Admin registered:", registerResponse.data.message);

    // Test 2: Register Employee User
    console.log("\n2. Testing Employee Registration...");
    const employeeRegisterResponse = await axios.post(
      `${BASE_URL}/api/auth/register`,
      {
        name: "Employee User",
        email: "<EMAIL>",
        password: "Employee123!",
        phone: "0987654321",
        role: "employee",
        department: "Sales",
        paymentMethod: "card",
      }
    );
    console.log(
      "✅ Employee registered:",
      employeeRegisterResponse.data.message
    );

    // Test 3: Admin Login (Step 1)
    console.log("\n3. Testing Admin Login...");
    const adminLoginResponse = await axios.post(
      `${BASE_URL}/api/auth/login-admin`,
      {
        email: "<EMAIL>",
        password: "Admin123!",
      }
    );
    console.log("✅ Admin login step 1:", adminLoginResponse.data.message);
    console.log("📱 Two-factor code should be logged in server console");

    // Test 4: Employee Login (Step 1)
    console.log("\n4. Testing Employee Login...");
    const employeeLoginResponse = await axios.post(
      `${BASE_URL}/api/auth/login-employee`,
      {
        email: "<EMAIL>",
        password: "Employee123!",
      }
    );
    console.log(
      "✅ Employee login step 1:",
      employeeLoginResponse.data.message
    );
    console.log("📱 Two-factor code should be logged in server console");

    // Test 5: Two-Factor Verification (using demo code)
    console.log("\n5. Testing Two-Factor Verification...");
    // Note: In real scenario, you'd get the code from the server logs
    // For testing, let's assume the code is what was generated
    const userId = adminLoginResponse.data.userId;

    // We'll need to check the server logs for the actual code
    console.log("⚠️  Check server logs for the 6-digit verification code");
    console.log("   Then test with: POST /api/auth/verify-code");
    console.log('   Body: { "userId": "' + userId + '", "code": "XXXXXX" }');
  } catch (error) {
    if (error.response) {
      console.error("❌ Error:", error.response.data.message);
    } else {
      console.error("❌ Network Error:", error.message);
    }
  }
}

// Run the test
testAuthFlow();
