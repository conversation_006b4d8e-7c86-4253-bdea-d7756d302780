const mongoose = require("mongoose");
require("dotenv").config();

// Import existing models
const Order = require("./models/Order");
const User = require("./models/User");
const Product = require("./models/Products");
const ReturnRequest = require("./models/Return");

async function createTestReturns() {
  try {
    // Connect to database
    await mongoose.connect(process.env.DB_URL, {
      dbName: process.env.DB_NAME || "ecommerce",
    });
    console.log("✅ Connected to MongoDB");

    // Using imported ReturnRequest model

    // Check existing returns
    const existingReturns = await ReturnRequest.find({});
    console.log(`📊 Found ${existingReturns.length} existing returns`);

    if (existingReturns.length > 0) {
      console.log("✅ Returns already exist. Displaying existing returns:");
      const populatedReturns = await ReturnRequest.find({})
        .populate("userId", "name email")
        .populate("orderId", "totalAmount")
        .limit(5);

      populatedReturns.forEach((returnReq, index) => {
        console.log(
          `   ${index + 1}. Return ${returnReq._id} - Status: ${
            returnReq.status
          } - User: ${returnReq.userId?.name}`
        );
      });
      return;
    }

    // Get existing orders, users, and products

    const orders = await Order.find({}).populate("user");
    const users = await User.find({});
    const products = await Product.find({});

    if (orders.length === 0 || users.length === 0 || products.length === 0) {
      console.log(
        "❌ No orders, users, or products found. Cannot create returns without all required data."
      );
      return;
    }

    console.log(
      `👥 Found ${users.length} users, ${orders.length} orders, and ${products.length} products`
    );

    // Create test returns
    const testReturns = [
      {
        orderId: orders[0]._id,
        userId: orders[0].user,
        productId: products[0]._id,
        reason: "Defective product - unusual taste and smell",
        status: "pending",
        comments:
          "The product had an unusual taste and smell. Upon opening the bottle, I noticed the liquid was cloudy and had a strange odor.",
        requestedAt: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000), // 2 days ago
      },
      {
        orderId: orders[1]._id,
        userId: orders[1].user,
        productId: products[1] ? products[1]._id : products[0]._id,
        reason: "Wrong item received",
        status: "approved",
        comments:
          "I ordered one product but received a completely different item. The packaging was correct but the product inside was wrong.",
        requestedAt: new Date(Date.now() - 3 * 24 * 60 * 60 * 1000), // 3 days ago
        processedAt: new Date(Date.now() - 1 * 24 * 60 * 60 * 1000), // 1 day ago
      },
      {
        orderId: orders[2]._id,
        userId: orders[2].user,
        productId: products[2] ? products[2]._id : products[0]._id,
        reason: "Not satisfied with quality",
        status: "completed",
        comments:
          "The product quality seems inferior compared to previous purchases. Different texture and color.",
        requestedAt: new Date(Date.now() - 5 * 24 * 60 * 60 * 1000), // 5 days ago
        processedAt: new Date(Date.now() - 1 * 24 * 60 * 60 * 1000), // 1 day ago
      },
      {
        orderId: orders[3]._id,
        userId: orders[3].user,
        productId: products[3] ? products[3]._id : products[0]._id,
        reason: "Damaged packaging",
        status: "pending",
        comments:
          "The package arrived with damaged packaging and some content had leaked out.",
        requestedAt: new Date(Date.now() - 1 * 24 * 60 * 60 * 1000), // 1 day ago
      },
    ];

    // Insert returns
    const createdReturns = await ReturnRequest.insertMany(testReturns);
    console.log(
      `✅ Created ${createdReturns.length} test returns successfully!`
    );

    // Display created returns
    const populatedReturns = await ReturnRequest.find({})
      .populate("userId", "name email")
      .populate("orderId", "totalAmount");

    populatedReturns.forEach((returnReq, index) => {
      console.log(
        `   ${index + 1}. Return ${returnReq._id} - Status: ${
          returnReq.status
        } - User: ${returnReq.userId?.name} - Reason: ${returnReq.reason}`
      );
    });

    console.log(
      "\n💡 Test returns created! You can now test the Returns page."
    );
  } catch (error) {
    console.error("❌ Error:", error.message);
  } finally {
    await mongoose.disconnect();
    console.log("🔌 Disconnected from MongoDB");
  }
}

createTestReturns();
