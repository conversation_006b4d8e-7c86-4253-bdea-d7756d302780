// Products API Service
import serviceWorkerAPI from "./serviceworker.api.js";

class ProductsAPI {
  // Get all products with optional filters
  async getProducts(filters = {}) {
    try {
      const response = await serviceWorkerAPI.getProducts(filters);
      console.log("Products fetched successfully:", response);
      return response;
    } catch (error) {
      console.error("Failed to fetch products:", error);
      throw error;
    }
  }

  // Get product by ID
  async getProductById(id) {
    try {
      const response = await serviceWorkerAPI.getProductById(id);
      console.log("Product fetched successfully:", response);
      return response;
    } catch (error) {
      console.error("Failed to fetch product:", error);
      throw error;
    }
  }

  // Create new product
  async createProduct(productData) {
    try {
      const response = await serviceWorkerAPI.createProduct(productData);
      console.log("Product created successfully:", response);
      return response;
    } catch (error) {
      console.error("Failed to create product:", error);
      throw error;
    }
  }

  // Update existing product
  async updateProduct(id, productData) {
    try {
      const response = await serviceWorkerAPI.updateProduct(id, productData);
      console.log("Product updated successfully:", response);
      return response;
    } catch (error) {
      console.error("Failed to update product:", error);
      throw error;
    }
  }

  // Delete product
  async deleteProduct(id) {
    try {
      const response = await serviceWorkerAPI.deleteProduct(id);
      console.log("Product deleted successfully:", response);
      return response;
    } catch (error) {
      console.error("Failed to delete product:", error);
      throw error;
    }
  }

  // Toggle product featured status
  async toggleFeaturedStatus(id) {
    try {
      const response = await serviceWorkerAPI.toggleProductFeatured(id);
      console.log("Product featured status toggled:", response);
      return response;
    } catch (error) {
      console.error("Failed to toggle featured status:", error);
      throw error;
    }
  }

  // Toggle product stock status
  async toggleStockStatus(id) {
    try {
      const response = await serviceWorkerAPI.toggleProductStock(id);
      console.log("Product stock status toggled:", response);
      return response;
    } catch (error) {
      console.error("Failed to toggle stock status:", error);
      throw error;
    }
  }

  // Get product statistics
  async getProductStats() {
    try {
      const response = await serviceWorkerAPI.getProductStats();
      console.log("Product stats fetched successfully:", response);
      return response;
    } catch (error) {
      console.error("Failed to fetch product stats:", error);
      throw error;
    }
  }

  // Get featured products
  async getFeaturedProducts(limit = 10) {
    try {
      const response = await serviceWorkerAPI.getFeaturedProducts(limit);
      console.log("Featured products fetched successfully:", response);
      return response;
    } catch (error) {
      console.error("Failed to fetch featured products:", error);
      throw error;
    }
  }

  // Upload product images
  async uploadProductImages(id, images) {
    try {
      const response = await serviceWorkerAPI.uploadImages(images);
      console.log("Product images uploaded successfully:", response);
      return response;
    } catch (error) {
      console.error("Failed to upload product images:", error);
      throw error;
    }
  }

  // Create product with images
  async createProductWithImages(productData, images) {
    try {
      // First create the product
      const productResponse = await this.createProduct(productData);

      // If images are provided, upload them
      if (images && images.length > 0 && productResponse.success) {
        const imageResponse = await this.uploadProductImages(
          productResponse.data.product._id,
          images
        );

        // Return combined response
        return {
          ...productResponse,
          images: imageResponse.data,
        };
      }

      return productResponse;
    } catch (error) {
      console.error("Failed to create product with images:", error);
      throw error;
    }
  }

  // Update product with images
  async updateProductWithImages(id, productData, images) {
    try {
      // First update the product
      const productResponse = await this.updateProduct(id, productData);

      // If images are provided, upload them
      if (images && images.length > 0 && productResponse.success) {
        const imageResponse = await this.uploadProductImages(id, images);

        // Return combined response
        return {
          ...productResponse,
          images: imageResponse.data,
        };
      }

      return productResponse;
    } catch (error) {
      console.error("Failed to update product with images:", error);
      throw error;
    }
  }

  // Search products
  async searchProducts(query, filters = {}) {
    try {
      const searchParams = {
        search: query,
        ...filters,
      };

      const response = await this.getProducts(searchParams);
      console.log("Product search completed:", response);
      return response;
    } catch (error) {
      console.error("Failed to search products:", error);
      throw error;
    }
  }

  // Get products by category
  async getProductsByCategory(categoryId, filters = {}) {
    try {
      const categoryParams = {
        categoryId,
        ...filters,
      };

      const response = await this.getProducts(categoryParams);
      console.log("Products by category fetched:", response);
      return response;
    } catch (error) {
      console.error("Failed to fetch products by category:", error);
      throw error;
    }
  }

  // Get low stock products
  async getLowStockProducts() {
    try {
      const response = await this.getProducts({ inStock: false });
      console.log("Low stock products fetched:", response);
      return response;
    } catch (error) {
      console.error("Failed to fetch low stock products:", error);
      throw error;
    }
  }

  // Bulk update products
  async bulkUpdateProducts(updates) {
    try {
      const promises = updates.map((update) =>
        this.updateProduct(update.id, update.data)
      );

      const responses = await Promise.allSettled(promises);

      const successful = responses.filter(
        (r) => r.status === "fulfilled"
      ).length;
      const failed = responses.filter((r) => r.status === "rejected").length;

      console.log(
        `Bulk update completed: ${successful} successful, ${failed} failed`
      );

      return {
        success: true,
        message: `Bulk update completed: ${successful} successful, ${failed} failed`,
        results: responses,
      };
    } catch (error) {
      console.error("Failed to bulk update products:", error);
      throw error;
    }
  }

  // Get product reviews
  async getProductReviews(productId) {
    try {
      // This would typically be a separate endpoint
      const response = await serviceWorkerAPI.get(
        `/api/products/${productId}/reviews`
      );
      console.log("Product reviews fetched:", response);
      return response;
    } catch (error) {
      console.error("Failed to fetch product reviews:", error);
      throw error;
    }
  }

  // Format product data for inventory display
  formatProductForInventory(product) {
    try {
      return {
        id: product._id || product.id,
        product: product.name || "Unknown Product",
        sku:
          product.sku ||
          `SKU-${
            product._id?.slice(-6) || Math.random().toString(36).substr(2, 6)
          }`,
        category: product.category?.name || product.category || "General",
        image: product.images?.[0]?.url || product.image || null,
        currentStock: product.stock || product.quantity || 0,
        minStock: product.minStock || 10,
        stockLevel: this.calculateStockLevel(
          product.stock || product.quantity || 0,
          product.minStock || 10
        ),
        value: this.calculateValue(
          product.stock || product.quantity || 0,
          product.price || 0
        ),
        price: `₹${product.price || 0}`,
        lastRestocked: product.lastRestocked
          ? new Date(product.lastRestocked).toLocaleDateString()
          : new Date().toLocaleDateString(),
        status: this.getStockStatus(
          product.stock || product.quantity || 0,
          product.minStock || 10
        ),
        batches: product.batches?.length?.toString() || "1",
      };
    } catch (error) {
      console.error("Error formatting product for inventory:", error);
      return {
        id:
          product._id || product.id || Math.random().toString(36).substr(2, 9),
        product: "Error Loading Product",
        sku: "ERROR",
        category: "Unknown",
        image: null,
        currentStock: 0,
        minStock: 10,
        stockLevel: "Unknown",
        value: "₹0",
        price: "₹0",
        lastRestocked: new Date().toLocaleDateString(),
        status: "error",
        batches: "0",
      };
    }
  }

  // Helper function to calculate stock level
  calculateStockLevel(currentStock, minStock) {
    if (currentStock === 0) return "Out of Stock";
    if (currentStock <= minStock) return "Low Stock";
    return "In Stock";
  }

  // Helper function to calculate total value
  calculateValue(stock, price) {
    const total = (stock || 0) * (price || 0);
    return total.toLocaleString("en-IN", {
      style: "currency",
      currency: "INR",
    });
  }

  // Helper function to get stock status
  getStockStatus(currentStock, minStock) {
    if (currentStock === 0) return "out of stock";
    if (currentStock <= minStock) return "low stock";
    return "in stock";
  }
}

// Create and export singleton instance
const productsAPI = new ProductsAPI();
export default productsAPI;

// Also export the class
export { ProductsAPI };
