require('dotenv').config();
const mongoose = require('mongoose');
const Notification = require('./models/Notification');
const User = require('./models/User');

async function createSampleNotifications() {
  try {
    // Connect to MongoDB
    await mongoose.connect(process.env.DB_URL + process.env.DB_NAME);
    console.log('✅ Connected to MongoDB');

    // Find admin user
    const adminUser = await User.findOne({ email: '<EMAIL>' });
    if (!adminUser) {
      console.log('❌ Admin user not found. Please create admin user first.');
      return;
    }

    console.log('👤 Found admin user:', adminUser.name);

    // Clear existing notifications
    await Notification.deleteMany({ user: adminUser._id });
    console.log('🗑️ Cleared existing notifications');

    // Sample notifications data
    const sampleNotifications = [
      {
        user: adminUser._id,
        title: 'Welcome to <PERSON><PERSON> Admin Panel',
        message: 'Welcome to the Dr. <PERSON> Laboratories Admin Panel! You now have access to all administrative features.',
        type: 'success',
        priority: 'high',
        category: 'system'
      },
      {
        user: adminUser._id,
        title: 'Low Stock Alert',
        message: 'Blood Test Kit inventory is running low. Only 5 units remaining.',
        type: 'alert',
        priority: 'urgent',
        category: 'alerts',
        data: { productId: 'blood-test-kit', currentStock: 5, minStock: 10 }
      },
      {
        user: adminUser._id,
        title: 'New Order Received',
        message: 'A new order #ORD-2024-001 has been placed by John Doe for ₹2,500.',
        type: 'info',
        priority: 'medium',
        category: 'updates',
        data: { orderId: 'ORD-2024-001', customerName: 'John Doe', amount: 2500 }
      },
      {
        user: adminUser._id,
        title: 'Monthly Report Available',
        message: 'Your monthly analytics report for January 2024 is now available for download.',
        type: 'report',
        priority: 'medium',
        category: 'reports',
        data: { reportType: 'monthly', month: 'January', year: 2024 }
      },
      {
        user: adminUser._id,
        title: 'System Maintenance Scheduled',
        message: 'Scheduled maintenance will occur on Sunday, 2:00 AM - 4:00 AM. Some features may be temporarily unavailable.',
        type: 'warning',
        priority: 'high',
        category: 'system',
        data: { maintenanceDate: '2024-02-04', startTime: '02:00', endTime: '04:00' }
      },
      {
        user: adminUser._id,
        title: 'Staff Member Added',
        message: 'New staff member Sarah Johnson has been successfully added to the system.',
        type: 'success',
        priority: 'low',
        category: 'updates',
        data: { staffName: 'Sarah Johnson', role: 'Lab Technician', department: 'Laboratory' }
      },
      {
        user: adminUser._id,
        title: 'Payment Received',
        message: 'Payment of ₹1,200 received for order #ORD-2024-002 from customer Maria Garcia.',
        type: 'success',
        priority: 'medium',
        category: 'updates',
        data: { orderId: 'ORD-2024-002', amount: 1200, customerName: 'Maria Garcia' }
      },
      {
        user: adminUser._id,
        title: 'Equipment Calibration Due',
        message: 'Blood analyzer equipment requires calibration. Last calibrated 90 days ago.',
        type: 'warning',
        priority: 'high',
        category: 'alerts',
        data: { equipment: 'Blood Analyzer', lastCalibration: '2023-11-01', daysOverdue: 90 }
      },
      {
        user: adminUser._id,
        title: 'Customer Feedback Received',
        message: 'New 5-star review received from customer Alex Thompson: "Excellent service and quick results!"',
        type: 'info',
        priority: 'low',
        category: 'updates',
        data: { customerName: 'Alex Thompson', rating: 5, review: 'Excellent service and quick results!' }
      },
      {
        user: adminUser._id,
        title: 'Database Backup Completed',
        message: 'Daily database backup completed successfully at 3:00 AM. All data is secure.',
        type: 'success',
        priority: 'low',
        category: 'system',
        data: { backupTime: '03:00', backupSize: '2.4GB', status: 'completed' }
      }
    ];

    // Create notifications with different timestamps
    for (let i = 0; i < sampleNotifications.length; i++) {
      const notification = sampleNotifications[i];
      
      // Create notifications with different timestamps (spread over last 7 days)
      const createdAt = new Date();
      createdAt.setDate(createdAt.getDate() - Math.floor(i / 2));
      createdAt.setHours(createdAt.getHours() - (i * 2));
      
      // Mark some notifications as read
      const isRead = i > 5; // First 6 notifications are unread
      
      const newNotification = new Notification({
        ...notification,
        read: isRead,
        readAt: isRead ? new Date(createdAt.getTime() + 3600000) : null, // 1 hour after creation
        createdAt
      });

      await newNotification.save();
    }

    console.log(`✅ Created ${sampleNotifications.length} sample notifications`);
    console.log('📊 Notification breakdown:');
    console.log('- Unread: 6 notifications');
    console.log('- Read: 4 notifications');
    console.log('- Alerts: 2 notifications');
    console.log('- Reports: 1 notification');
    console.log('- Updates: 5 notifications');
    console.log('- System: 2 notifications');

  } catch (error) {
    console.error('❌ Error creating sample notifications:', error);
  } finally {
    await mongoose.disconnect();
    console.log('🔌 Disconnected from MongoDB');
  }
}

createSampleNotifications();
