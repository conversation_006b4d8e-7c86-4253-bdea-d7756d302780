const http = require('http');
const mongoose = require('mongoose');
const User = require('./models/User');
require('dotenv').config();

function makeRequest(options, data) {
    return new Promise((resolve, reject) => {
        const req = http.request(options, (res) => {
            let body = '';
            res.on('data', (chunk) => {
                body += chunk;
            });
            res.on('end', () => {
                try {
                    const jsonBody = JSON.parse(body);
                    resolve({ status: res.statusCode, data: jsonBody });
                } catch (e) {
                    resolve({ status: res.statusCode, data: body });
                }
            });
        });

        req.on('error', (err) => {
            reject(err);
        });

        if (data) {
            req.write(JSON.stringify(data));
        }
        req.end();
    });
}

async function getTwoFactorCode(email) {
    await mongoose.connect(process.env.DB_URL, {
        dbName: process.env.DB_NAME || 'ecommerce',
    });
    
    const user = await User.findOne({ email });
    const code = user ? user.twoFactorCode : null;
    
    await mongoose.connection.close();
    return code;
}

async function testEmployeeFlow() {
    console.log('🧪 Testing Employee Authentication Flow...\n');

    try {
        // Step 1: Employee Login
        console.log('1. Testing Employee Login...');
        const loginOptions = {
            hostname: 'localhost',
            port: 5000,
            path: '/api/auth/login-employee',
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            }
        };

        const loginData = {
            email: '<EMAIL>',
            password: 'Employee123!'
        };

        const loginResponse = await makeRequest(loginOptions, loginData);
        console.log('✅ Login Status:', loginResponse.status);
        console.log('📝 Login Response:', loginResponse.data);

        if (loginResponse.data.success && loginResponse.data.userId) {
            // Step 2: Get the two-factor code from database
            console.log('\n2. Getting two-factor code from database...');
            const twoFactorCode = await getTwoFactorCode('<EMAIL>');
            console.log('🔐 Two-Factor Code:', twoFactorCode);

            if (twoFactorCode) {
                // Step 3: Verify two-factor code
                console.log('\n3. Testing Two-Factor Verification...');
                const verifyOptions = {
                    hostname: 'localhost',
                    port: 5000,
                    path: '/api/auth/verify-code',
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    }
                };

                const verifyData = {
                    userId: loginResponse.data.userId,
                    code: twoFactorCode
                };

                const verifyResponse = await makeRequest(verifyOptions, verifyData);
                console.log('✅ Verify Status:', verifyResponse.status);
                console.log('📝 Verify Response:', verifyResponse.data);

                if (verifyResponse.data.success && verifyResponse.data.token) {
                    console.log('\n🎉 EMPLOYEE AUTHENTICATION FLOW SUCCESSFUL!');
                    console.log('🎟️  JWT Token:', verifyResponse.data.token);
                    console.log('👤 User Info:', verifyResponse.data.user);
                } else {
                    console.log('❌ Two-factor verification failed');
                }
            } else {
                console.log('❌ No two-factor code found');
            }
        } else {
            console.log('❌ Employee login failed');
        }

    } catch (error) {
        console.error('❌ Error:', error.message);
    }
}

testEmployeeFlow();
