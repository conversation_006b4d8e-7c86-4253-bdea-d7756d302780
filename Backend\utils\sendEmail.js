const nodemailer = require("nodemailer");

// Create email transporter
const createTransporter = () => {
  return nodemailer.createTransport({
    service: "gmail",
    auth: {
      user: process.env.EMAIL_USER,
      pass: process.env.EMAIL_PASS,
    },
  });
};

// Generate branded email template
const generateEmailTemplate = (content, logoUrl = "") => {
  return `
    <!DOCTYPE html>
    <html lang="en">
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>Dr. <PERSON></title>
        <style>
            body {
                font-family: 'Arial', sans-serif;
                line-height: 1.6;
                color: #333;
                max-width: 600px;
                margin: 0 auto;
                padding: 20px;
                background-color: #f4f4f4;
            }
            .email-container {
                background-color: #ffffff;
                border-radius: 10px;
                padding: 30px;
                box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            }
            .header {
                text-align: center;
                margin-bottom: 30px;
                border-bottom: 2px solid #e0e0e0;
                padding-bottom: 20px;
            }
            .logo {
                max-width: 150px;
                height: auto;
                margin-bottom: 15px;
            }
            .company-name {
                font-size: 24px;
                font-weight: bold;
                color: #2c3e50;
                margin: 0;
            }
            .content {
                margin: 20px 0;
            }
            .verification-code {
                background-color: #3498db;
                color: white;
                font-size: 32px;
                font-weight: bold;
                text-align: center;
                padding: 20px;
                border-radius: 8px;
                margin: 20px 0;
                letter-spacing: 3px;
            }
            .footer {
                margin-top: 30px;
                padding-top: 20px;
                border-top: 1px solid #e0e0e0;
                text-align: center;
                font-size: 12px;
                color: #666;
            }
            .contact-info {
                margin-top: 15px;
            }
            .btn {
                display: inline-block;
                background-color: #27ae60;
                color: white;
                padding: 12px 25px;
                text-decoration: none;
                border-radius: 5px;
                margin: 10px 0;
                font-weight: bold;
            }
        </style>
    </head>
    <body>
        <div class="email-container">
            <div class="header">
                ${
                  logoUrl
                    ? `<img src="${logoUrl}" alt="Dr. Kumar Laboratories" class="logo">`
                    : ""
                }
                <h1 class="company-name">Dr. Kumar Laboratories</h1>
            </div>
            <div class="content">
                ${content}
            </div>
            <div class="footer">
                <div class="contact-info">
                    <p><strong>Dr. Kumar Laboratories</strong></p>
                    <p>📧 Email: <EMAIL></p>
                    <p>📞 Phone: +91 9876543210</p>
                    <p>🌐 Website: https://drkumarlaboratories.com</p>
                </div>
                <p style="margin-top: 20px;">
                    This is an automated message. Please do not reply to this email.
                </p>
            </div>
        </div>
    </body>
    </html>
    `;
};

// Send Two-Factor Authentication Code
const sendTwoFactorEmail = async (to, code, userName = "") => {
  try {
    const transporter = createTransporter();

    const content = `
            <h2>Dr. Kumar Laboratories Login Verification</h2>
            <p>Hello ${userName ? userName : "User"},</p>
            <p>Your One-Time Verification Code is:</p>
            <div class="verification-code">${code}</div>
            <p>This code will expire in <strong>10 minutes</strong>.</p>
            <p>If this wasn't you, please ignore this email.</p>
            <br>
            <p>Thank you,<br>Dr. Kumar Laboratories</p>
        `;

    const htmlContent = generateEmailTemplate(content);

    await transporter.sendMail({
      from: `"Dr. Kumar Laboratories" <${process.env.EMAIL_USER}>`,
      to,
      subject: "Dr. Kumar Laboratories Login Verification Code",
      html: htmlContent,
    });

    console.log(`✅ Two-factor email sent to ${to}`);
    return true;
  } catch (error) {
    console.error("Two-factor email sending error:", error);
    return false;
  }
};

// Send Staff Invitation Email
const sendStaffInvitationEmail = async (
  to,
  staffName,
  role,
  signupUrl = "https://drkumarlaboratories.com/auth/signup"
) => {
  try {
    const transporter = createTransporter();

    const logoUrl = "https://i.imgur.com/your-logo-url.png"; // Replace with actual logo URL

    const content = `
            <h2>You've been invited to join Dr. Kumar Admin Panel!</h2>
            <p>Dear ${staffName},</p>
            <p>You've been invited to join the Dr. Kumar Admin Panel as a <strong>${role}</strong>.</p>
            <p>To get started, please click the button below to set your password and access your account:</p>
            <div style="text-align: center; margin: 30px 0;">
                <a href="${signupUrl}" class="btn" style="background-color: #27ae60; color: white; padding: 15px 30px; text-decoration: none; border-radius: 5px; font-weight: bold; display: inline-block;">Set Password & Access Account</a>
            </div>
            <p><strong>What's Next:</strong></p>
            <ul>
                <li>Click the button above to set your password</li>
                <li>Complete your profile setup</li>
                <li>Start using the admin dashboard</li>
            </ul>
            <p>If you have any questions, please contact your administrator.</p>
            <p><strong>Note:</strong> This invitation link is secure and personalized for you.</p>
        `;

    const htmlContent = generateEmailTemplate(content, logoUrl);

    await transporter.sendMail({
      from: `"Dr. Kumar Laboratories" <${process.env.EMAIL_USER}>`,
      to,
      subject: "🎉 Invitation to Dr. Kumar Admin Panel - Set Your Password",
      html: htmlContent,
    });

    console.log(`✅ Staff invitation email sent to ${to}`);
    return true;
  } catch (error) {
    console.error("Staff invitation email sending error:", error);
    return false;
  }
};

// Send Password Reset Email
const sendPasswordResetEmail = async (
  to,
  userName,
  resetUrl = "https://drkumarlaboratories.com/auth/reset-password"
) => {
  try {
    const transporter = createTransporter();

    // Use the same logo URL as other emails for consistency
    const logoUrl = "https://i.imgur.com/your-logo-url.png"; // Replace with actual Dr. Kumar logo URL

    const content = `
            <h2>Password Reset Request</h2>
            <p>Dear ${userName},</p>
            <p>We received a request to reset your password for your Dr. Kumar Admin account. If you made this request, click the button below to create a new password. If you did not request a password reset, you can safely ignore this email.</p>
            <div style="text-align: center; margin: 30px 0;">
                <a href="${resetUrl}" class="btn" style="background-color: #e74c3c; color: white; padding: 15px 30px; text-decoration: none; border-radius: 5px; font-weight: bold; display: inline-block;">Create New Password</a>
            </div>
            <p><strong>Important Security Information:</strong></p>
            <ul>
                <li>This link will expire in 1 hour for security reasons</li>
                <li>If you didn't request this reset, please ignore this email</li>
                <li>Your current password will remain unchanged until you create a new one</li>
                <li>Only use this link if you requested a password reset</li>
            </ul>
            <p>If you have any questions or concerns, please contact your administrator.</p>
            <p><strong>Note:</strong> This reset link is secure and personalized for your account.</p>
        `;

    const htmlContent = generateEmailTemplate(content, logoUrl);

    console.log(`📧 Attempting to send password reset email to: ${to}`);
    console.log(`🔗 Reset URL: ${resetUrl}`);

    await transporter.sendMail({
      from: `"Dr. Kumar Laboratories" <${process.env.EMAIL_USER}>`,
      to,
      subject: "🔐 Password Reset Request - Dr. Kumar Admin Panel",
      html: htmlContent,
    });

    console.log(`✅ Password reset email sent successfully to ${to}`);
    return true;
  } catch (error) {
    console.error("❌ Password reset email sending error:", error);
    console.error("Email config check:", {
      EMAIL_USER: process.env.EMAIL_USER ? "Set" : "Not set",
      EMAIL_PASS: process.env.EMAIL_PASS ? "Set" : "Not set",
    });
    return false;
  }
};

// Send Welcome Email for New Staff
const sendWelcomeEmail = async (
  to,
  staffName,
  loginUrl = "https://drkumarlaboratories.com"
) => {
  try {
    const transporter = createTransporter();

    const content = `
            <h2>Welcome to Dr. Kumar Laboratories!</h2>
            <p>Dear ${staffName},</p>
            <p>Welcome to the Dr. Kumar Laboratories team! We're excited to have you on board.</p>
            <p>Your staff account has been successfully created. You can now access the admin portal using your email address and the password provided by your administrator.</p>
            <a href="${loginUrl}" class="btn">Access Admin Portal</a>
            <p><strong>Getting Started:</strong></p>
            <ul>
                <li>Use your email address to log in</li>
                <li>You'll receive a two-factor authentication code via email</li>
                <li>Complete your profile setup after first login</li>
                <li>Contact IT support if you need assistance</li>
            </ul>
            <p>We look forward to working with you!</p>
            <p>Best regards,<br>Dr. Kumar Laboratories Team</p>
        `;

    const htmlContent = generateEmailTemplate(content);

    await transporter.sendMail({
      from: `"Dr. Kumar Laboratories" <${process.env.EMAIL_USER}>`,
      to,
      subject: "🎉 Welcome to Dr. Kumar Laboratories - Your Account is Ready!",
      html: htmlContent,
    });

    console.log(`✅ Welcome email sent to ${to}`);
    return true;
  } catch (error) {
    console.error("Welcome email sending error:", error);
    return false;
  }
};

// Helper function to send basic email (backward compatibility)
const sendEmail = async (to, subject, text) => {
  try {
    const transporter = createTransporter();

    await transporter.sendMail({
      from: `"Dr. Kumar Laboratories" <${process.env.EMAIL_USER}>`,
      to,
      subject,
      text,
    });

    console.log(`✅ Email sent to ${to}`);
    return true;
  } catch (error) {
    console.error("Email sending error:", error);
    throw new Error("Failed to send email");
  }
};

module.exports = {
  sendEmail,
  sendTwoFactorEmail,
  sendWelcomeEmail,
  sendStaffInvitationEmail,
  sendPasswordResetEmail,
  generateEmailTemplate,
};
