const nodemailer = require("nodemailer");

// Create email transporter
const createTransporter = () => {
  return nodemailer.createTransport({
    service: "gmail",
    auth: {
      user: process.env.EMAIL_USER,
      pass: process.env.EMAIL_PASS,
    },
  });
};

// Generate branded email template
const generateEmailTemplate = (content, logoUrl = "") => {
  return `
    <!DOCTYPE html>
    <html lang="en">
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>Dr. <PERSON></title>
        <style>
            body {
                font-family: 'Arial', sans-serif;
                line-height: 1.6;
                color: #333;
                max-width: 600px;
                margin: 0 auto;
                padding: 20px;
                background-color: #f4f4f4;
            }
            .email-container {
                background-color: #ffffff;
                border-radius: 10px;
                padding: 30px;
                box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            }
            .header {
                text-align: center;
                margin-bottom: 30px;
                border-bottom: 2px solid #e0e0e0;
                padding-bottom: 20px;
            }
            .logo {
                max-width: 150px;
                height: auto;
                margin-bottom: 15px;
            }
            .company-name {
                font-size: 24px;
                font-weight: bold;
                color: #2c3e50;
                margin: 0;
            }
            .content {
                margin: 20px 0;
            }
            .verification-code {
                background-color: #3498db;
                color: white;
                font-size: 32px;
                font-weight: bold;
                text-align: center;
                padding: 20px;
                border-radius: 8px;
                margin: 20px 0;
                letter-spacing: 3px;
            }
            .footer {
                margin-top: 30px;
                padding-top: 20px;
                border-top: 1px solid #e0e0e0;
                text-align: center;
                font-size: 12px;
                color: #666;
            }
            .contact-info {
                margin-top: 15px;
            }
            .btn {
                display: inline-block;
                background-color: #27ae60;
                color: white;
                padding: 12px 25px;
                text-decoration: none;
                border-radius: 5px;
                margin: 10px 0;
                font-weight: bold;
            }
        </style>
    </head>
    <body>
        <div class="email-container">
            <div class="header">
                ${
                  logoUrl
                    ? `<img src="${logoUrl}" alt="Dr. Kumar Laboratories" class="logo">`
                    : ""
                }
                <h1 class="company-name">Dr. Kumar Laboratories</h1>
            </div>
            <div class="content">
                ${content}
            </div>
            <div class="footer">
                <div class="contact-info">
                    <p><strong>Dr. Kumar Laboratories</strong></p>
                    <p>📧 Email: <EMAIL></p>
                    <p>📞 Phone: +91 9876543210</p>
                    <p>🌐 Website: https://drkumarlaboratories.com</p>
                </div>
                <p style="margin-top: 20px;">
                    This is an automated message. Please do not reply to this email.
                </p>
            </div>
        </div>
    </body>
    </html>
    `;
};

// Send Two-Factor Authentication Code
const sendTwoFactorEmail = async (to, code, userName = "") => {
  try {
    const transporter = createTransporter();

    const content = `
            <h2>Dr. Kumar Laboratories Login Verification</h2>
            <p>Hello ${userName ? userName : "User"},</p>
            <p>Your One-Time Verification Code is:</p>
            <div class="verification-code">${code}</div>
            <p>This code will expire in <strong>10 minutes</strong>.</p>
            <p>If this wasn't you, please ignore this email.</p>
            <br>
            <p>Thank you,<br>Dr. Kumar Laboratories</p>
        `;

    const htmlContent = generateEmailTemplate(content);

    await transporter.sendMail({
      from: `"Dr. Kumar Laboratories" <${process.env.EMAIL_USER}>`,
      to,
      subject: "Dr. Kumar Laboratories Login Verification Code",
      html: htmlContent,
    });

    console.log(`✅ Two-factor email sent to ${to}`);
    return true;
  } catch (error) {
    console.error("Two-factor email sending error:", error);
    return false;
  }
};

// Send Staff Invitation Email
const sendStaffInvitationEmail = async (
  to,
  staffName,
  role,
  signupUrl = "https://drkumarlaboratories.com/auth/signup"
) => {
  try {
    const transporter = createTransporter();

    // Use the actual Dr. Kumar logo from the frontend
    const logoUrl =
      "https://raw.githubusercontent.com/your-repo/main/Frontend/public/lovable-uploads/079f933a-4d0a-4e30-bf46-582b2b7fd97b.png";

    const content = `
            <div style="text-align: center; margin-bottom: 30px;">
                <h2 style="color: #2c3e50; margin-bottom: 10px;">🎉 Welcome to Dr. Kumar Admin Panel!</h2>
                <p style="color: #7f8c8d; font-size: 16px;">You've been invited to join our team</p>
            </div>

            <div style="background-color: #f8f9fa; padding: 20px; border-radius: 8px; margin: 20px 0;">
                <p>Dear <strong>${staffName}</strong>,</p>
                <p>Congratulations! You've been invited to join the <strong>Dr. Kumar Admin Panel</strong> as a <strong style="color: #27ae60;">${role}</strong>.</p>
                <p>This invitation gives you access to our comprehensive admin dashboard where you can manage operations, view analytics, and collaborate with the team.</p>
            </div>

            <div style="text-align: center; margin: 40px 0;">
                <a href="${signupUrl}" style="background: linear-gradient(135deg, #27ae60, #2ecc71); color: white; padding: 16px 32px; text-decoration: none; border-radius: 8px; font-weight: bold; display: inline-block; box-shadow: 0 4px 12px rgba(46, 204, 113, 0.3); transition: all 0.3s ease;">
                    🔐 Set Password & Access Account
                </a>
            </div>

            <div style="background-color: #e8f5e8; padding: 20px; border-radius: 8px; border-left: 4px solid #27ae60;">
                <h3 style="color: #27ae60; margin-top: 0;">📋 Getting Started:</h3>
                <ol style="color: #2c3e50; line-height: 1.8;">
                    <li><strong>Click the button above</strong> to set your secure password</li>
                    <li><strong>Complete your profile</strong> with additional details</li>
                    <li><strong>Explore the dashboard</strong> and familiarize yourself with the tools</li>
                    <li><strong>Start collaborating</strong> with your team members</li>
                </ol>
            </div>

            <div style="margin: 30px 0; padding: 15px; background-color: #fff3cd; border-radius: 6px; border-left: 4px solid #ffc107;">
                <p style="margin: 0; color: #856404;"><strong>🔒 Security Note:</strong> This invitation link is secure and personalized for you. It will expire after first use for your protection.</p>
            </div>

            <p style="color: #6c757d;">If you have any questions or need assistance, please don't hesitate to contact your administrator or our support team.</p>

            <div style="text-align: center; margin-top: 30px; padding-top: 20px; border-top: 1px solid #dee2e6;">
                <p style="color: #6c757d; margin: 0;">Welcome to the Dr. Kumar Laboratories family! 🏥</p>
            </div>
        `;

    const htmlContent = generateEmailTemplate(content, logoUrl);

    await transporter.sendMail({
      from: `"Dr. Kumar Laboratories" <${process.env.EMAIL_USER}>`,
      to,
      subject: "🎉 Invitation to Dr. Kumar Admin Panel - Set Your Password",
      html: htmlContent,
    });

    console.log(`✅ Staff invitation email sent to ${to}`);
    return true;
  } catch (error) {
    console.error("Staff invitation email sending error:", error);
    return false;
  }
};

// Send Password Reset Email
const sendPasswordResetEmail = async (
  to,
  userName,
  resetUrl = "https://drkumarlaboratories.com/auth/reset-password"
) => {
  try {
    const transporter = createTransporter();

    // Use the actual Dr. Kumar logo from the frontend
    const logoUrl =
      "https://raw.githubusercontent.com/your-repo/main/Frontend/public/lovable-uploads/079f933a-4d0a-4e30-bf46-582b2b7fd97b.png";

    const content = `
            <div style="text-align: center; margin-bottom: 30px;">
                <h2 style="color: #e74c3c; margin-bottom: 10px;">🔐 Password Reset Request</h2>
                <p style="color: #7f8c8d; font-size: 16px;">Secure password reset for your account</p>
            </div>

            <div style="background-color: #f8f9fa; padding: 20px; border-radius: 8px; margin: 20px 0;">
                <p>Dear <strong>${userName}</strong>,</p>
                <p>We received a request to reset your password for your <strong>Dr. Kumar Admin Panel</strong> account.</p>
                <p>If you requested this password reset, please click the button below to create a new secure password:</p>
            </div>

            <div style="text-align: center; margin: 40px 0;">
                <a href="${resetUrl}" style="background: linear-gradient(135deg, #e74c3c, #c0392b); color: white; padding: 16px 32px; text-decoration: none; border-radius: 8px; font-weight: bold; display: inline-block; box-shadow: 0 4px 12px rgba(231, 76, 60, 0.3); transition: all 0.3s ease;">
                    🔑 Create New Password
                </a>
            </div>

            <div style="background-color: #fff3cd; padding: 20px; border-radius: 8px; border-left: 4px solid #ffc107;">
                <h3 style="color: #856404; margin-top: 0;">⚠️ Important Security Information:</h3>
                <ul style="color: #856404; line-height: 1.8; margin: 0;">
                    <li>This reset link will <strong>expire in 1 hour</strong> for your security</li>
                    <li>If you didn't request this reset, please <strong>ignore this email</strong></li>
                    <li>Your current password will remain unchanged until you create a new one</li>
                    <li>Only use this link if you personally requested a password reset</li>
                </ul>
            </div>

            <div style="background-color: #d1ecf1; padding: 15px; border-radius: 6px; border-left: 4px solid #17a2b8; margin: 20px 0;">
                <p style="margin: 0; color: #0c5460;"><strong>💡 Didn't request this?</strong> If you didn't request a password reset, your account is still secure. You can safely ignore this email.</p>
            </div>

            <p style="color: #6c757d;">If you have any questions or concerns about your account security, please contact your administrator or our support team immediately.</p>

            <div style="text-align: center; margin-top: 30px; padding-top: 20px; border-top: 1px solid #dee2e6;">
                <p style="color: #6c757d; margin: 0;">Stay secure with Dr. Kumar Laboratories 🔒</p>
            </div>
        `;

    const htmlContent = generateEmailTemplate(content, logoUrl);

    console.log(`📧 Attempting to send password reset email to: ${to}`);
    console.log(`🔗 Reset URL: ${resetUrl}`);

    await transporter.sendMail({
      from: `"Dr. Kumar Laboratories" <${process.env.EMAIL_USER}>`,
      to,
      subject: "🔐 Password Reset Request - Dr. Kumar Admin Panel",
      html: htmlContent,
    });

    console.log(`✅ Password reset email sent successfully to ${to}`);
    return true;
  } catch (error) {
    console.error("❌ Password reset email sending error:", error);
    console.error("Email config check:", {
      EMAIL_USER: process.env.EMAIL_USER ? "Set" : "Not set",
      EMAIL_PASS: process.env.EMAIL_PASS ? "Set" : "Not set",
    });
    return false;
  }
};

// Send Welcome Email for New Staff
const sendWelcomeEmail = async (
  to,
  staffName,
  loginUrl = "https://drkumarlaboratories.com"
) => {
  try {
    const transporter = createTransporter();

    const content = `
            <h2>Welcome to Dr. Kumar Laboratories!</h2>
            <p>Dear ${staffName},</p>
            <p>Welcome to the Dr. Kumar Laboratories team! We're excited to have you on board.</p>
            <p>Your staff account has been successfully created. You can now access the admin portal using your email address and the password provided by your administrator.</p>
            <a href="${loginUrl}" class="btn">Access Admin Portal</a>
            <p><strong>Getting Started:</strong></p>
            <ul>
                <li>Use your email address to log in</li>
                <li>You'll receive a two-factor authentication code via email</li>
                <li>Complete your profile setup after first login</li>
                <li>Contact IT support if you need assistance</li>
            </ul>
            <p>We look forward to working with you!</p>
            <p>Best regards,<br>Dr. Kumar Laboratories Team</p>
        `;

    const htmlContent = generateEmailTemplate(content);

    await transporter.sendMail({
      from: `"Dr. Kumar Laboratories" <${process.env.EMAIL_USER}>`,
      to,
      subject: "🎉 Welcome to Dr. Kumar Laboratories - Your Account is Ready!",
      html: htmlContent,
    });

    console.log(`✅ Welcome email sent to ${to}`);
    return true;
  } catch (error) {
    console.error("Welcome email sending error:", error);
    return false;
  }
};

// Helper function to send basic email (backward compatibility)
const sendEmail = async (to, subject, text) => {
  try {
    const transporter = createTransporter();

    await transporter.sendMail({
      from: `"Dr. Kumar Laboratories" <${process.env.EMAIL_USER}>`,
      to,
      subject,
      text,
    });

    console.log(`✅ Email sent to ${to}`);
    return true;
  } catch (error) {
    console.error("Email sending error:", error);
    throw new Error("Failed to send email");
  }
};

module.exports = {
  sendEmail,
  sendTwoFactorEmail,
  sendWelcomeEmail,
  sendStaffInvitationEmail,
  sendPasswordResetEmail,
  generateEmailTemplate,
};
