const User = require("../models/User");
const bcrypt = require("bcryptjs");
const {
  sendWelcomeEmail,
  sendStaffInvitationEmail,
} = require("../utils/sendEmail");

// @desc    Get all staff members
// @route   GET /api/admin/staff
// @access  Private (Admin only)
const getAllStaff = async (req, res) => {
  try {
    const {
      page = 1,
      limit = 10,
      search = "",
      status = "",
      department = "",
      role = "",
    } = req.query;

    // Build filter object
    const filter = {
      role: { $in: ["admin", "employee"] }, // Only get admin and employee users
    };

    // Add search filter
    if (search) {
      filter.$or = [
        { name: { $regex: search, $options: "i" } },
        { email: { $regex: search, $options: "i" } },
        { department: { $regex: search, $options: "i" } },
      ];
    }

    // Add status filter
    if (status) {
      filter.status = status;
    }

    // Add department filter
    if (department) {
      filter.department = { $regex: department, $options: "i" };
    }

    // Add role filter
    if (role && role !== "all") {
      filter.role = role;
    }

    // Calculate pagination
    const skip = (parseInt(page) - 1) * parseInt(limit);

    // Get staff with pagination
    const staff = await User.find(filter)
      .select("-password -twoFactorCode")
      .sort({ createdAt: -1 })
      .skip(skip)
      .limit(parseInt(limit));

    // Get total count for pagination
    const totalStaff = await User.countDocuments(filter);
    const totalPages = Math.ceil(totalStaff / parseInt(limit));

    // Get statistics
    const stats = {
      total: await User.countDocuments({
        role: { $in: ["admin", "employee"] },
      }),
      active: await User.countDocuments({
        role: { $in: ["admin", "employee"] },
        status: "active",
      }),
      inactive: await User.countDocuments({
        role: { $in: ["admin", "employee"] },
        status: "inactive",
      }),
      departments: await User.distinct("department", {
        role: { $in: ["admin", "employee"] },
      }),
    };

    res.json({
      success: true,
      message: "Staff retrieved successfully",
      data: {
        staff,
        pagination: {
          page: parseInt(page),
          limit: parseInt(limit),
          totalPages,
          totalItems: totalStaff,
          hasNextPage: page < totalPages,
          hasPrevPage: page > 1,
        },
        stats: {
          ...stats,
          departmentCount: stats.departments.length,
        },
      },
    });
  } catch (error) {
    console.error("Get all staff error:", error);
    res.status(500).json({
      success: false,
      message: "Server error while retrieving staff",
      error: error.message,
    });
  }
};

// @desc    Get staff member by ID
// @route   GET /api/admin/staff/:id
// @access  Private (Admin only)
const getStaffById = async (req, res) => {
  try {
    const { id } = req.params;

    const staff = await User.findById(id).select("-password -twoFactorCode");

    if (!staff) {
      return res.status(404).json({
        success: false,
        message: "Staff member not found",
      });
    }

    // Check if user is staff (admin or employee)
    if (!["admin", "employee"].includes(staff.role)) {
      return res.status(400).json({
        success: false,
        message: "User is not a staff member",
      });
    }

    res.json({
      success: true,
      message: "Staff member retrieved successfully",
      data: { staff },
    });
  } catch (error) {
    console.error("Get staff by ID error:", error);
    res.status(500).json({
      success: false,
      message: "Server error while retrieving staff member",
      error: error.message,
    });
  }
};

// @desc    Create new staff member
// @route   POST /api/admin/staff
// @access  Private (Admin only)
const createStaff = async (req, res) => {
  try {
    const {
      name,
      email,
      password,
      phone,
      role,
      department,
      position,
      status = "active",
    } = req.body;

    // Validate required fields (password is no longer required for invitation flow)
    if (!name || !email || !role) {
      return res.status(400).json({
        success: false,
        message: "Please provide all required fields: name, email, role",
      });
    }

    // Validate role
    if (!["admin", "employee"].includes(role)) {
      return res.status(400).json({
        success: false,
        message: "Role must be either admin or employee",
      });
    }

    // Check if user already exists
    const existingUser = await User.findOne({ email });

    if (existingUser) {
      return res.status(400).json({
        success: false,
        message: "User with this email already exists",
      });
    }

    // Clean and validate phone number
    let cleanPhone = "";
    if (phone && phone.trim()) {
      cleanPhone = phone.replace(/\D/g, ""); // Remove all non-digit characters
      if (cleanPhone.length < 10 || cleanPhone.length > 15) {
        return res.status(400).json({
          success: false,
          message: "Phone number must be between 10-15 digits",
        });
      }
    }

    // Create new staff member without password (invitation flow)
    const newStaff = new User({
      name,
      email,
      phone: cleanPhone,
      role,
      department: department || "General",
      position: position || role,
      status,
      isEmailVerified: false, // Will be verified when they set password
      isPasswordSet: false, // Flag to indicate password needs to be set
      joinedDate: new Date(),
      paymentMethod: "card", // Default payment method
    });

    await newStaff.save();

    // Send invitation email
    try {
      const signupUrl = `${
        process.env.FRONTEND_URL || "http://localhost:8081"
      }/auth/signup?email=${encodeURIComponent(email)}`;
      await sendStaffInvitationEmail(email, name, role, signupUrl);
      console.log(`✅ Invitation email sent to new staff member: ${email}`);
    } catch (emailError) {
      console.error("Failed to send invitation email:", emailError);
      // Don't fail the staff creation if email fails
    }

    // Return staff data without password
    const staffData = await User.findById(newStaff._id).select(
      "-password -twoFactorCode"
    );

    res.status(201).json({
      success: true,
      message: "Staff member invited successfully. Invitation email sent.",
      data: { staff: staffData },
    });
  } catch (error) {
    console.error("Create staff error:", error);
    res.status(500).json({
      success: false,
      message: "Server error while creating staff member",
      error: error.message,
    });
  }
};

// @desc    Update staff member
// @route   PUT /api/admin/staff/:id
// @access  Private (Admin only)
const updateStaff = async (req, res) => {
  try {
    const { id } = req.params;
    const updateData = req.body;

    // Remove sensitive fields that shouldn't be updated via this endpoint
    delete updateData.password;
    delete updateData.twoFactorCode;
    delete updateData.twoFactorExpires;

    // Validate role if provided
    if (updateData.role && !["admin", "employee"].includes(updateData.role)) {
      return res.status(400).json({
        success: false,
        message: "Role must be either admin or employee",
      });
    }

    // Check if staff member exists
    const existingStaff = await User.findById(id);
    if (!existingStaff) {
      return res.status(404).json({
        success: false,
        message: "Staff member not found",
      });
    }

    // Check if user is staff
    if (!["admin", "employee"].includes(existingStaff.role)) {
      return res.status(400).json({
        success: false,
        message: "User is not a staff member",
      });
    }

    // Check for email/phone conflicts if they're being updated
    if (updateData.email || updateData.phone) {
      const conflictFilter = { _id: { $ne: id } };
      if (updateData.email) conflictFilter.email = updateData.email;
      if (updateData.phone) conflictFilter.phone = updateData.phone;

      const conflictUser = await User.findOne(conflictFilter);
      if (conflictUser) {
        return res.status(400).json({
          success: false,
          message: "Another user with this email or phone already exists",
        });
      }
    }

    // Update staff member
    const updatedStaff = await User.findByIdAndUpdate(
      id,
      { ...updateData, updatedAt: new Date() },
      { new: true, runValidators: true }
    ).select("-password -twoFactorCode");

    res.json({
      success: true,
      message: "Staff member updated successfully",
      data: { staff: updatedStaff },
    });
  } catch (error) {
    console.error("Update staff error:", error);
    res.status(500).json({
      success: false,
      message: "Server error while updating staff member",
      error: error.message,
    });
  }
};

// @desc    Delete staff member
// @route   DELETE /api/admin/staff/:id
// @access  Private (Admin only)
const deleteStaff = async (req, res) => {
  try {
    const { id } = req.params;

    // Check if staff member exists
    const staff = await User.findById(id);
    if (!staff) {
      return res.status(404).json({
        success: false,
        message: "Staff member not found",
      });
    }

    // Check if user is staff
    if (!["admin", "employee"].includes(staff.role)) {
      return res.status(400).json({
        success: false,
        message: "User is not a staff member",
      });
    }

    // Prevent deletion of the current user
    if (staff._id.toString() === req.user.id) {
      return res.status(400).json({
        success: false,
        message: "You cannot delete your own account",
      });
    }

    // Delete staff member
    await User.findByIdAndDelete(id);

    res.json({
      success: true,
      message: "Staff member deleted successfully",
    });
  } catch (error) {
    console.error("Delete staff error:", error);
    res.status(500).json({
      success: false,
      message: "Server error while deleting staff member",
      error: error.message,
    });
  }
};

// @desc    Update staff status
// @route   PUT /api/admin/staff/:id/status
// @access  Private (Admin only)
const updateStaffStatus = async (req, res) => {
  try {
    const { id } = req.params;
    const { status } = req.body;

    // Validate status
    if (!["active", "inactive", "banned"].includes(status)) {
      return res.status(400).json({
        success: false,
        message: "Status must be active, inactive, or banned",
      });
    }

    // Check if staff member exists
    const staff = await User.findById(id);
    if (!staff) {
      return res.status(404).json({
        success: false,
        message: "Staff member not found",
      });
    }

    // Check if user is staff
    if (!["admin", "employee"].includes(staff.role)) {
      return res.status(400).json({
        success: false,
        message: "User is not a staff member",
      });
    }

    // Prevent status change of the current user
    if (staff._id.toString() === req.user.id) {
      return res.status(400).json({
        success: false,
        message: "You cannot change your own status",
      });
    }

    // Update status
    const updatedStaff = await User.findByIdAndUpdate(
      id,
      { status, updatedAt: new Date() },
      { new: true, runValidators: true }
    ).select("-password -twoFactorCode");

    res.json({
      success: true,
      message: `Staff status updated to ${status}`,
      data: { staff: updatedStaff },
    });
  } catch (error) {
    console.error("Update staff status error:", error);
    res.status(500).json({
      success: false,
      message: "Server error while updating staff status",
      error: error.message,
    });
  }
};

module.exports = {
  getAllStaff,
  getStaffById,
  createStaff,
  updateStaff,
  deleteStaff,
  updateStaffStatus,
};
