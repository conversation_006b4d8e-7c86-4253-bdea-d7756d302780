const Cart = require('../models/Cart');
const Product = require('../models/Products');

// @desc    Get user's cart
// @route   GET /api/cart
// @access  Private
const getCart = async (req, res) => {
    try {
        let cart = await Cart.findOne({ user: req.user.userId })
            .populate('items.product', 'name price images inStock active');

        if (!cart) {
            // Create empty cart if none exists
            cart = new Cart({
                user: req.user.userId,
                items: []
            });
            await cart.save();
        }

        // Filter out inactive or out-of-stock products
        const validItems = cart.items.filter(item => 
            item.product && item.product.active && item.product.inStock
        );

        // Update cart if items were filtered out
        if (validItems.length !== cart.items.length) {
            cart.items = validItems;
            await cart.save();
        }

        // Calculate cart totals
        const cartTotal = cart.items.reduce((total, item) => {
            return total + (item.product.price * item.quantity);
        }, 0);

        const itemCount = cart.items.reduce((count, item) => {
            return count + item.quantity;
        }, 0);

        res.json({
            success: true,
            cart: {
                ...cart.toObject(),
                cartTotal,
                itemCount
            }
        });
    } catch (error) {
        res.status(500).json({
            success: false,
            message: 'Server error fetching cart',
            error: error.message
        });
    }
};

// @desc    Add item to cart
// @route   POST /api/cart/add
// @access  Private
const addToCart = async (req, res) => {
    try {
        const { productId, quantity = 1 } = req.body;

        if (!productId) {
            return res.status(400).json({
                success: false,
                message: 'Product ID is required'
            });
        }

        if (quantity < 1) {
            return res.status(400).json({
                success: false,
                message: 'Quantity must be at least 1'
            });
        }

        // Check if product exists and is available
        const product = await Product.findById(productId);
        if (!product) {
            return res.status(404).json({
                success: false,
                message: 'Product not found'
            });
        }

        if (!product.active || !product.inStock) {
            return res.status(400).json({
                success: false,
                message: 'Product is not available'
            });
        }

        // Find or create cart
        let cart = await Cart.findOne({ user: req.user.userId });
        if (!cart) {
            cart = new Cart({
                user: req.user.userId,
                items: []
            });
        }

        // Check if item already exists in cart
        const existingItemIndex = cart.items.findIndex(
            item => item.product.toString() === productId
        );

        if (existingItemIndex > -1) {
            // Update quantity if item exists
            cart.items[existingItemIndex].quantity += parseInt(quantity);
        } else {
            // Add new item to cart
            cart.items.push({
                product: productId,
                quantity: parseInt(quantity)
            });
        }

        await cart.save();

        // Populate and return updated cart
        const populatedCart = await Cart.findById(cart._id)
            .populate('items.product', 'name price images inStock active');

        // Calculate cart totals
        const cartTotal = populatedCart.items.reduce((total, item) => {
            return total + (item.product.price * item.quantity);
        }, 0);

        const itemCount = populatedCart.items.reduce((count, item) => {
            return count + item.quantity;
        }, 0);

        res.json({
            success: true,
            message: 'Item added to cart successfully',
            cart: {
                ...populatedCart.toObject(),
                cartTotal,
                itemCount
            }
        });
    } catch (error) {
        res.status(500).json({
            success: false,
            message: 'Server error adding item to cart',
            error: error.message
        });
    }
};

// @desc    Update cart item quantity
// @route   PUT /api/cart/update/:itemId
// @access  Private
const updateCartItem = async (req, res) => {
    try {
        const { quantity } = req.body;

        if (!quantity || quantity < 1) {
            return res.status(400).json({
                success: false,
                message: 'Quantity must be at least 1'
            });
        }

        const cart = await Cart.findOne({ user: req.user.userId });
        if (!cart) {
            return res.status(404).json({
                success: false,
                message: 'Cart not found'
            });
        }

        const itemIndex = cart.items.findIndex(
            item => item._id.toString() === req.params.itemId
        );

        if (itemIndex === -1) {
            return res.status(404).json({
                success: false,
                message: 'Item not found in cart'
            });
        }

        // Check if product is still available
        const product = await Product.findById(cart.items[itemIndex].product);
        if (!product || !product.active || !product.inStock) {
            return res.status(400).json({
                success: false,
                message: 'Product is no longer available'
            });
        }

        cart.items[itemIndex].quantity = parseInt(quantity);
        await cart.save();

        // Populate and return updated cart
        const populatedCart = await Cart.findById(cart._id)
            .populate('items.product', 'name price images inStock active');

        // Calculate cart totals
        const cartTotal = populatedCart.items.reduce((total, item) => {
            return total + (item.product.price * item.quantity);
        }, 0);

        const itemCount = populatedCart.items.reduce((count, item) => {
            return count + item.quantity;
        }, 0);

        res.json({
            success: true,
            message: 'Cart item updated successfully',
            cart: {
                ...populatedCart.toObject(),
                cartTotal,
                itemCount
            }
        });
    } catch (error) {
        res.status(500).json({
            success: false,
            message: 'Server error updating cart item',
            error: error.message
        });
    }
};

// @desc    Remove item from cart
// @route   DELETE /api/cart/remove/:itemId
// @access  Private
const removeFromCart = async (req, res) => {
    try {
        const cart = await Cart.findOne({ user: req.user.userId });
        if (!cart) {
            return res.status(404).json({
                success: false,
                message: 'Cart not found'
            });
        }

        const itemIndex = cart.items.findIndex(
            item => item._id.toString() === req.params.itemId
        );

        if (itemIndex === -1) {
            return res.status(404).json({
                success: false,
                message: 'Item not found in cart'
            });
        }

        cart.items.splice(itemIndex, 1);
        await cart.save();

        // Populate and return updated cart
        const populatedCart = await Cart.findById(cart._id)
            .populate('items.product', 'name price images inStock active');

        // Calculate cart totals
        const cartTotal = populatedCart.items.reduce((total, item) => {
            return total + (item.product.price * item.quantity);
        }, 0);

        const itemCount = populatedCart.items.reduce((count, item) => {
            return count + item.quantity;
        }, 0);

        res.json({
            success: true,
            message: 'Item removed from cart successfully',
            cart: {
                ...populatedCart.toObject(),
                cartTotal,
                itemCount
            }
        });
    } catch (error) {
        res.status(500).json({
            success: false,
            message: 'Server error removing item from cart',
            error: error.message
        });
    }
};

// @desc    Clear cart
// @route   DELETE /api/cart/clear
// @access  Private
const clearCart = async (req, res) => {
    try {
        const cart = await Cart.findOne({ user: req.user.userId });
        if (!cart) {
            return res.status(404).json({
                success: false,
                message: 'Cart not found'
            });
        }

        cart.items = [];
        await cart.save();

        res.json({
            success: true,
            message: 'Cart cleared successfully',
            cart: {
                ...cart.toObject(),
                cartTotal: 0,
                itemCount: 0
            }
        });
    } catch (error) {
        res.status(500).json({
            success: false,
            message: 'Server error clearing cart',
            error: error.message
        });
    }
};

// @desc    Get cart summary
// @route   GET /api/cart/summary
// @access  Private
const getCartSummary = async (req, res) => {
    try {
        const cart = await Cart.findOne({ user: req.user.userId })
            .populate('items.product', 'name price images');

        if (!cart || cart.items.length === 0) {
            return res.json({
                success: true,
                summary: {
                    itemCount: 0,
                    subtotal: 0,
                    estimatedTax: 0,
                    total: 0,
                    items: []
                }
            });
        }

        // Filter out inactive or out-of-stock products
        const validItems = cart.items.filter(item => 
            item.product && item.product.active && item.product.inStock
        );

        const subtotal = validItems.reduce((total, item) => {
            return total + (item.product.price * item.quantity);
        }, 0);

        const itemCount = validItems.reduce((count, item) => {
            return count + item.quantity;
        }, 0);

        // Calculate estimated tax (10% for example)
        const taxRate = 0.10;
        const estimatedTax = subtotal * taxRate;
        const total = subtotal + estimatedTax;

        res.json({
            success: true,
            summary: {
                itemCount,
                subtotal: Math.round(subtotal * 100) / 100,
                estimatedTax: Math.round(estimatedTax * 100) / 100,
                total: Math.round(total * 100) / 100,
                items: validItems.map(item => ({
                    productId: item.product._id,
                    name: item.product.name,
                    price: item.product.price,
                    quantity: item.quantity,
                    itemTotal: Math.round(item.product.price * item.quantity * 100) / 100
                }))
            }
        });
    } catch (error) {
        res.status(500).json({
            success: false,
            message: 'Server error fetching cart summary',
            error: error.message
        });
    }
};

// @desc    Validate cart items
// @route   GET /api/cart/validate
// @access  Private
const validateCart = async (req, res) => {
    try {
        const cart = await Cart.findOne({ user: req.user.userId })
            .populate('items.product', 'name price images inStock active');

        if (!cart) {
            return res.json({
                success: true,
                valid: true,
                issues: []
            });
        }

        const issues = [];
        const validItems = [];

        for (const item of cart.items) {
            if (!item.product) {
                issues.push({
                    type: 'product_not_found',
                    message: 'Product no longer exists',
                    itemId: item._id
                });
                continue;
            }

            if (!item.product.active) {
                issues.push({
                    type: 'product_inactive',
                    message: `Product "${item.product.name}" is no longer available`,
                    itemId: item._id,
                    productId: item.product._id
                });
                continue;
            }

            if (!item.product.inStock) {
                issues.push({
                    type: 'product_out_of_stock',
                    message: `Product "${item.product.name}" is currently out of stock`,
                    itemId: item._id,
                    productId: item.product._id
                });
                continue;
            }

            validItems.push(item);
        }

        // Remove invalid items if any
        if (issues.length > 0) {
            cart.items = validItems;
            await cart.save();
        }

        res.json({
            success: true,
            valid: issues.length === 0,
            issues,
            validItemCount: validItems.length
        });
    } catch (error) {
        res.status(500).json({
            success: false,
            message: 'Server error validating cart',
            error: error.message
        });
    }
};

// @desc    Merge guest cart with user cart
// @route   POST /api/cart/merge
// @access  Private
const mergeCart = async (req, res) => {
    try {
        const { guestCartItems } = req.body;

        if (!guestCartItems || !Array.isArray(guestCartItems)) {
            return res.status(400).json({
                success: false,
                message: 'Guest cart items are required'
            });
        }

        // Find or create user cart
        let userCart = await Cart.findOne({ user: req.user.userId });
        if (!userCart) {
            userCart = new Cart({
                user: req.user.userId,
                items: []
            });
        }

        // Merge guest cart items
        for (const guestItem of guestCartItems) {
            // Validate product exists and is available
            const product = await Product.findById(guestItem.productId);
            if (!product || !product.active || !product.inStock) {
                continue; // Skip invalid products
            }

            // Check if item already exists in user cart
            const existingItemIndex = userCart.items.findIndex(
                item => item.product.toString() === guestItem.productId
            );

            if (existingItemIndex > -1) {
                // Update quantity if item exists
                userCart.items[existingItemIndex].quantity += parseInt(guestItem.quantity);
            } else {
                // Add new item to cart
                userCart.items.push({
                    product: guestItem.productId,
                    quantity: parseInt(guestItem.quantity)
                });
            }
        }

        await userCart.save();

        // Populate and return merged cart
        const populatedCart = await Cart.findById(userCart._id)
            .populate('items.product', 'name price images inStock active');

        // Calculate cart totals
        const cartTotal = populatedCart.items.reduce((total, item) => {
            return total + (item.product.price * item.quantity);
        }, 0);

        const itemCount = populatedCart.items.reduce((count, item) => {
            return count + item.quantity;
        }, 0);

        res.json({
            success: true,
            message: 'Guest cart merged successfully',
            cart: {
                ...populatedCart.toObject(),
                cartTotal,
                itemCount
            }
        });
    } catch (error) {
        res.status(500).json({
            success: false,
            message: 'Server error merging cart',
            error: error.message
        });
    }
};

// @desc    Get cart item count
// @route   GET /api/cart/count
// @access  Private
const getCartCount = async (req, res) => {
    try {
        const cart = await Cart.findOne({ user: req.user.userId })
            .populate('items.product', 'active inStock');

        if (!cart) {
            return res.json({
                success: true,
                count: 0
            });
        }

        // Filter out inactive or out-of-stock products
        const validItems = cart.items.filter(item => 
            item.product && item.product.active && item.product.inStock
        );

        const itemCount = validItems.reduce((count, item) => {
            return count + item.quantity;
        }, 0);

        res.json({
            success: true,
            count: itemCount
        });
    } catch (error) {
        res.status(500).json({
            success: false,
            message: 'Server error fetching cart count',
            error: error.message
        });
    }
};

module.exports = {
    getCart,
    addToCart,
    updateCartItem,
    removeFromCart,
    clearCart,
    getCartSummary,
    validateCart,
    mergeCart,
    getCartCount
};