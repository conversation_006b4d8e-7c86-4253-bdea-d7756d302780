const express = require('express');
const router = express.Router();
const {
    createBanner,
    updateBanner,
    deleteBanner,
    toggleBannerStatus,
    updateBannerPriority,
    getBannerStats,
    getExpiringSoonBanners,
    bulkUpdateBanners,
    getBanners,
    getActiveBanners,
    getBannerById
} = require('../../controllers/bannerController');
const { protect, authorize } = require('../../middleware/auth');
const multer = require('multer');
const upload = multer({ dest: 'uploads/' });

// All routes are protected and require admin access
router.use(protect);
router.use(authorize('admin'));

// @route   POST /api/admin/banners
// @desc    Create new banner
// @access  Private/Admin
router.post('/', upload.single('image'), createBanner);

// @route   PUT /api/admin/banners/:id
// @desc    Update banner
// @access  Private/Admin
router.put('/:id', upload.single('image'), updateBanner);

// @route   DELETE /api/admin/banners/:id
// @desc    Delete banner
// @access  Private/Admin
router.delete('/:id', deleteBanner);

// @route   PUT /api/admin/banners/:id/toggle-active
// @desc    Toggle banner active status
// @access  Private/Admin
router.put('/:id/toggle-active', toggleBannerStatus);

// @route   PUT /api/admin/banners/:id/priority
// @desc    Update banner priority
// @access  Private/Admin
router.put('/:id/priority', updateBannerPriority);

// @route   GET /api/admin/banners/stats
// @desc    Get banner statistics
// @access  Private/Admin
router.get('/stats', getBannerStats);

// @route   GET /api/admin/banners/expiring-soon
// @desc    Get expiring soon banners
// @access  Private/Admin
router.get('/expiring-soon', getExpiringSoonBanners);

// @route   PUT /api/admin/banners/bulk-update
// @desc    Bulk update banners
// @access  Private/Admin
router.put('/bulk-update', bulkUpdateBanners);

// @route   GET /api/admin/banners
// @desc    Get all banners
// @access  Private/Admin
router.get('/', getBanners);

// @route   GET /api/admin/banners/active
// @desc    Get all active banners
// @access  Private/Admin
router.get('/active', getActiveBanners);

// @route   GET /api/admin/banners/:id
// @desc    Get banner by ID
// @access  Private/Admin
router.get('/:id', getBannerById);

module.exports = router;
