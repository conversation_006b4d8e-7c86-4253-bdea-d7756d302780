// Coupons API Service
import serviceWorkerAPI from "./serviceworker.api.js";

class CouponsAPI {
  // Get all coupons with optional filters
  async getCoupons(filters = {}) {
    try {
      const response = await serviceWorkerAPI.getCoupons(filters);
      console.log("Coupons fetched successfully:", response);
      return response;
    } catch (error) {
      console.error("Failed to fetch coupons:", error);
      throw error;
    }
  }

  // Create new coupon
  async createCoupon(couponData) {
    try {
      const response = await serviceWorkerAPI.createCoupon(couponData);
      console.log("Coupon created successfully:", response);
      return response;
    } catch (error) {
      console.error("Failed to create coupon:", error);
      throw error;
    }
  }

  // Update coupon
  async updateCoupon(id, couponData) {
    try {
      const response = await serviceWorkerAPI.updateCoupon(id, couponData);
      console.log("Coupon updated successfully:", response);
      return response;
    } catch (error) {
      console.error("Failed to update coupon:", error);
      throw error;
    }
  }

  // Validate coupon
  async validateCoupon(code) {
    try {
      const response = await serviceWorkerAPI.validateCoupon(code);
      console.log("Coupon validated:", response);
      return response;
    } catch (error) {
      console.error("Failed to validate coupon:", error);
      throw error;
    }
  }

  // Format coupon for display
  formatCouponForDisplay(coupon) {
    try {
      return {
        id: coupon._id || coupon.id,
        code: coupon.code || 'UNKNOWN',
        description: coupon.description || 'No description available',
        type: coupon.type || coupon.discountType || 'Percentage',
        value: coupon.value || coupon.discountValue || 0,
        minOrder: coupon.minOrder || coupon.minimumOrderValue || 0,
        maxDiscount: coupon.maxDiscount || coupon.maximumDiscount || 0,
        usageLimit: coupon.usageLimit || coupon.maxUses || 0,
        used: coupon.used || coupon.usedCount || 0,
        status: this.getCouponStatus(coupon),
        validFrom: this.formatDate(coupon.validFrom || coupon.startDate),
        validTo: this.formatDate(coupon.validTo || coupon.endDate),
        productName: coupon.productName || coupon.applicableProducts?.[0]?.name || 'All Products',
        category: coupon.category || coupon.applicableCategories?.[0]?.name || 'All Categories',
        isActive: coupon.isActive !== undefined ? coupon.isActive : true,
        createdAt: coupon.createdAt,
        updatedAt: coupon.updatedAt
      };
    } catch (error) {
      console.error('Error formatting coupon:', error);
      return {
        id: coupon._id || coupon.id || Math.random().toString(36).substr(2, 9),
        code: 'ERROR',
        description: 'Error loading coupon',
        type: 'Percentage',
        value: 0,
        minOrder: 0,
        maxDiscount: 0,
        usageLimit: 0,
        used: 0,
        status: 'Inactive',
        validFrom: new Date().toISOString().split('T')[0],
        validTo: new Date().toISOString().split('T')[0],
        productName: 'Unknown',
        category: 'Unknown',
        isActive: false,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
      };
    }
  }

  // Get coupon status
  getCouponStatus(coupon) {
    try {
      if (!coupon.isActive) return 'Inactive';
      
      const now = new Date();
      const startDate = new Date(coupon.validFrom || coupon.startDate);
      const endDate = new Date(coupon.validTo || coupon.endDate);
      
      if (now < startDate) return 'Scheduled';
      if (now > endDate) return 'Expired';
      
      const usageLimit = coupon.usageLimit || coupon.maxUses || 0;
      const used = coupon.used || coupon.usedCount || 0;
      
      if (usageLimit > 0 && used >= usageLimit) return 'Exhausted';
      
      return 'Active';
    } catch (error) {
      console.error('Error getting coupon status:', error);
      return 'Unknown';
    }
  }

  // Format date for display
  formatDate(dateString) {
    try {
      if (!dateString) return new Date().toISOString().split('T')[0];
      const date = new Date(dateString);
      return date.toISOString().split('T')[0];
    } catch (error) {
      console.error('Error formatting date:', error);
      return new Date().toISOString().split('T')[0];
    }
  }

  // Calculate coupon statistics
  calculateCouponStats(coupons) {
    try {
      const stats = {
        total: coupons.length,
        active: 0,
        inactive: 0,
        expired: 0,
        totalRedemption: 0
      };

      coupons.forEach(coupon => {
        switch (coupon.status) {
          case 'Active':
            stats.active++;
            break;
          case 'Inactive':
          case 'Exhausted':
            stats.inactive++;
            break;
          case 'Expired':
            stats.expired++;
            break;
        }

        // Calculate total redemption value (approximate)
        const redemptionValue = coupon.used * (coupon.value || 0);
        stats.totalRedemption += redemptionValue;
      });

      return [
        { title: "Total Coupons", value: stats.total.toString(), color: "text-gray-900" },
        { title: "Active", value: stats.active.toString(), color: "text-green-600" },
        { title: "Inactive", value: stats.inactive.toString(), color: "text-red-600" },
        { title: "Total Redemption", value: `₹${stats.totalRedemption.toLocaleString()}`, color: "text-blue-600" },
      ];
    } catch (error) {
      console.error('Error calculating coupon stats:', error);
      return [
        { title: "Total Coupons", value: "0", color: "text-gray-900" },
        { title: "Active", value: "0", color: "text-green-600" },
        { title: "Inactive", value: "0", color: "text-red-600" },
        { title: "Total Redemption", value: "₹0", color: "text-blue-600" },
      ];
    }
  }

  // Get status color for UI
  getStatusColor(status) {
    switch (status) {
      case 'Active':
        return 'bg-green-100 text-green-700';
      case 'Inactive':
        return 'bg-gray-100 text-gray-700';
      case 'Expired':
        return 'bg-red-100 text-red-700';
      case 'Exhausted':
        return 'bg-orange-100 text-orange-700';
      case 'Scheduled':
        return 'bg-blue-100 text-blue-700';
      default:
        return 'bg-gray-100 text-gray-700';
    }
  }

  // Filter coupons by status
  filterCouponsByStatus(coupons, status) {
    if (status === 'all') return coupons;
    return coupons.filter(coupon => coupon.status.toLowerCase() === status.toLowerCase());
  }
}

const couponsAPI = new CouponsAPI();
export default couponsAPI;
