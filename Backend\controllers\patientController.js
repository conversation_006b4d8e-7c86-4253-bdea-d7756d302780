const Patient = require('../models/Patient');

// @desc    Create new patient
// @route   POST /api/patients
// @access  Private/Admin/Employee
const createPatient = async (req, res) => {
    try {
        const {
            name,
            age,
            gender,
            contactNumber,
            address,
            healthConditions,
            appointmentDetails,
            decription,
            nextDueAppointment
        } = req.body;

        // Validate required fields
        if (!name || !age || !gender || !contactNumber) {
            return res.status(400).json({
                success: false,
                message: 'Name, age, gender, and contact number are required'
            });
        }

        // Check if patient already exists with same contact number
        const existingPatient = await Patient.findOne({ contactNumber });
        if (existingPatient) {
            return res.status(400).json({
                success: false,
                message: 'Patient with this contact number already exists'
            });
        }

        // Validate health conditions format
        if (healthConditions && Array.isArray(healthConditions)) {
            for (const condition of healthConditions) {
                if (!condition.condition) {
                    return res.status(400).json({
                        success: false,
                        message: 'Health condition name is required'
                    });
                }
            }
        }

        // Validate appointment details format
        if (appointmentDetails && Array.isArray(appointmentDetails)) {
            for (const appointment of appointmentDetails) {
                if (!appointment.appointmentDate) {
                    return res.status(400).json({
                        success: false,
                        message: 'Appointment date is required'
                    });
                }
            }
        }

        const patient = new Patient({
            name,
            age,
            gender,
            contactNumber,
            address,
            healthConditions: healthConditions || [],
            appointmentDetails: appointmentDetails || [],
            decription: decription || '',
            nextDueAppointment: nextDueAppointment || null
        });

        await patient.save();

        res.status(201).json({
            success: true,
            message: 'Patient created successfully',
            patient
        });
    } catch (error) {
        res.status(500).json({
            success: false,
            message: 'Server error creating patient',
            error: error.message
        });
    }
};

// @desc    Get all patients
// @route   GET /api/patients
// @access  Private/Admin/Employee
const getPatients = async (req, res) => {
    try {
        const { 
            page = 1, 
            limit = 10, 
            search, 
            gender, 
            ageMin, 
            ageMax,
            appointmentStatus,
            hasUpcomingAppointment,
            sort = 'name'
        } = req.query;

        // Build query object
        const query = {};
        
        if (search) {
            query.$or = [
                { name: { $regex: search, $options: 'i' } },
                { contactNumber: { $regex: search, $options: 'i' } },
                { address: { $regex: search, $options: 'i' } }
            ];
        }

        if (gender) query.gender = gender;
        
        if (ageMin || ageMax) {
            query.age = {};
            if (ageMin) query.age.$gte = parseInt(ageMin);
            if (ageMax) query.age.$lte = parseInt(ageMax);
        }

        if (appointmentStatus) {
            query['appointmentDetails.status'] = appointmentStatus;
        }

        if (hasUpcomingAppointment === 'true') {
            query.nextDueAppointment = { $gte: new Date() };
        } else if (hasUpcomingAppointment === 'false') {
            query.$or = [
                { nextDueAppointment: null },
                { nextDueAppointment: { $lt: new Date() } }
            ];
        }

        // Build sort object
        const sortObject = {};
        if (sort === 'name') {
            sortObject.name = 1;
        } else if (sort === 'age') {
            sortObject.age = 1;
        } else if (sort === 'newest') {
            sortObject.createdAt = -1;
        } else if (sort === 'oldest') {
            sortObject.createdAt = 1;
        } else if (sort === 'nextAppointment') {
            sortObject.nextDueAppointment = 1;
        } else {
            sortObject.name = 1; // Default sort
        }

        const patients = await Patient.find(query)
            .sort(sortObject)
            .limit(limit * 1)
            .skip((page - 1) * limit);

        const total = await Patient.countDocuments(query);

        res.json({
            success: true,
            patients,
            pagination: {
                page: parseInt(page),
                limit: parseInt(limit),
                total,
                pages: Math.ceil(total / limit)
            }
        });
    } catch (error) {
        res.status(500).json({
            success: false,
            message: 'Server error fetching patients',
            error: error.message
        });
    }
};

// @desc    Get patient by ID
// @route   GET /api/patients/:id
// @access  Private/Admin/Employee
const getPatientById = async (req, res) => {
    try {
        const patient = await Patient.findById(req.params.id);

        if (!patient) {
            return res.status(404).json({
                success: false,
                message: 'Patient not found'
            });
        }
        res.json({
            success: true,
            patient
        });
    } catch (error) {
        res.status(500).json({
            success: false,
            message: 'Server error fetching patient',
            error: error.message
        });
    }
};

// @desc    Update patient
// @route   PUT /api/patients/:id
// @access  Private/Admin/Employee
const updatePatient = async (req, res) => {
    try {
        const {
            name,
            age,
            gender,
            contactNumber,
            address,
            healthConditions,
            appointmentDetails,
            decription,
            nextDueAppointment
        } = req.body;

        const patient = await Patient.findById(req.params.id);
        if (!patient) {
            return res.status(404).json({
                success: false,
                message: 'Patient not found'
            });
        }

        // Check if contact number is being updated and if it already exists
        if (contactNumber && contactNumber !== patient.contactNumber) {
            const existingPatient = await Patient.findOne({ 
                contactNumber,
                _id: { $ne: req.params.id }
            });
            if (existingPatient) {
                return res.status(400).json({
                    success: false,
                    message: 'Patient with this contact number already exists'
                });
            }
        }

        // Validate health conditions format if provided
        if (healthConditions && Array.isArray(healthConditions)) {
            for (const condition of healthConditions) {
                if (!condition.condition) {
                    return res.status(400).json({
                        success: false,
                        message: 'Health condition name is required'
                    });
                }
            }
        }

        // Validate appointment details format if provided
        if (appointmentDetails && Array.isArray(appointmentDetails)) {
            for (const appointment of appointmentDetails) {
                if (!appointment.appointmentDate) {
                    return res.status(400).json({
                        success: false,
                        message: 'Appointment date is required'
                    });
                }
            }
        }

        // Update fields if provided
        if (name !== undefined) patient.name = name;
        if (age !== undefined) patient.age = age;
        if (gender !== undefined) patient.gender = gender;
        if (contactNumber !== undefined) patient.contactNumber = contactNumber;
        if (address !== undefined) patient.address = address;
        if (healthConditions !== undefined) patient.healthConditions = healthConditions;
        if (appointmentDetails !== undefined) patient.appointmentDetails = appointmentDetails;
        if (decription !== undefined) patient.decription = decription;
        if (nextDueAppointment !== undefined) patient.nextDueAppointment = nextDueAppointment;

        await patient.save();

        res.json({
            success: true,
            message: 'Patient updated successfully',
            patient
        });
    } catch (error) {
        res.status(500).json({
            success: false,
            message: 'Server error updating patient',
            error: error.message
        });
    }
};

// @desc    Delete patient
// @route   DELETE /api/patients/:id
// @access  Private/Admin
const deletePatient = async (req, res) => {
    try {
        const patient = await Patient.findById(req.params.id);
        if (!patient) {
            return res.status(404).json({
                success: false,
                message: 'Patient not found'
            });
        }

        await Patient.findByIdAndDelete(req.params.id);

        res.json({
            success: true,
            message: 'Patient deleted successfully'
        });
    } catch (error) {
        res.status(500).json({
            success: false,
            message: 'Server error deleting patient',
            error: error.message
        });
    }
};

// @desc    Add health condition to patient
// @route   POST /api/patients/:id/health-conditions
// @access  Private/Admin/Employee
const addHealthCondition = async (req, res) => {
    try {
        const { condition, diagnosedDate, notes } = req.body;

        if (!condition) {
            return res.status(400).json({
                success: false,
                message: 'Health condition name is required'
            });
        }

        const patient = await Patient.findById(req.params.id);
        if (!patient) {
            return res.status(404).json({
                success: false,
                message: 'Patient not found'
            });
        }

        // Check if condition already exists
        const existingCondition = patient.healthConditions.find(
            hc => hc.condition.toLowerCase() === condition.toLowerCase()
        );

        if (existingCondition) {
            return res.status(400).json({
                success: false,
                message: 'This health condition already exists for the patient'
            });
        }

        const newHealthCondition = {
            condition,
            diagnosedDate: diagnosedDate || new Date(),
            notes: notes || ''
        };

        patient.healthConditions.push(newHealthCondition);
        await patient.save();

        res.status(201).json({
            success: true,
            message: 'Health condition added successfully',
            patient
        });
    } catch (error) {
        res.status(500).json({
            success: false,
            message: 'Server error adding health condition',
            error: error.message
        });
    }
};

// @desc    Update health condition
// @route   PUT /api/patients/:id/health-conditions/:conditionId
// @access  Private/Admin/Employee
const updateHealthCondition = async (req, res) => {
    try {
        const { condition, diagnosedDate, notes } = req.body;

        const patient = await Patient.findById(req.params.id);
        if (!patient) {
            return res.status(404).json({
                success: false,
                message: 'Patient not found'
            });
        }

        const healthCondition = patient.healthConditions.id(req.params.conditionId);
        if (!healthCondition) {
            return res.status(404).json({
                success: false,
                message: 'Health condition not found'
            });
        }

        // Update fields if provided
        if (condition !== undefined) healthCondition.condition = condition;
        if (diagnosedDate !== undefined) healthCondition.diagnosedDate = diagnosedDate;
        if (notes !== undefined) healthCondition.notes = notes;

        await patient.save();

        res.json({
            success: true,
            message: 'Health condition updated successfully',
            patient
        });
    } catch (error) {
        res.status(500).json({
            success: false,
            message: 'Server error updating health condition',
            error: error.message
        });
    }
};

// @desc    Delete health condition
// @route   DELETE /api/patients/:id/health-conditions/:conditionId
// @access  Private/Admin/Employee
const deleteHealthCondition = async (req, res) => {
    try {
        const patient = await Patient.findById(req.params.id);
        if (!patient) {
            return res.status(404).json({
                success: false,
                message: 'Patient not found'
            });
        }

        const healthCondition = patient.healthConditions.id(req.params.conditionId);
        if (!healthCondition) {
            return res.status(404).json({
                success: false,
                message: 'Health condition not found'
            });
        }

        patient.healthConditions.pull(req.params.conditionId);
        await patient.save();

        res.json({
            success: true,
            message: 'Health condition deleted successfully'
        });
    } catch (error) {
        res.status(500).json({
            success: false,
            message: 'Server error deleting health condition',
            error: error.message
        });
    }
};

// @desc    Schedule appointment
// @route   POST /api/patients/:id/appointments
// @access  Private/Admin/Employee
const scheduleAppointment = async (req, res) => {
    try {
        const { appointmentDate, notes } = req.body;

        if (!appointmentDate) {
            return res.status(400).json({
                success: false,
                message: 'Appointment date is required'
            });
        }

        const patient = await Patient.findById(req.params.id);
        if (!patient) {
            return res.status(404).json({
                success: false,
                message: 'Patient not found'
            });
        }

        // Check if appointment already exists for the same date
        const existingAppointment = patient.appointmentDetails.find(
            appointment => new Date(appointment.appointmentDate).toDateString() === new Date(appointmentDate).toDateString()
        );

        if (existingAppointment) {
            return res.status(400).json({
                success: false,
                message: 'Appointment already scheduled for this date'
            });
        }

        const newAppointment = {
            appointmentDate: new Date(appointmentDate),
            status: 'Scheduled',
            notes: notes || ''
        };

        patient.appointmentDetails.push(newAppointment);
        
        // Update next due appointment if this is the earliest future appointment
        const futureAppointments = patient.appointmentDetails.filter(
            app => new Date(app.appointmentDate) > new Date() && app.status === 'Scheduled'
        );
        
        if (futureAppointments.length > 0) {
            const nextAppointment = futureAppointments.sort(
                (a, b) => new Date(a.appointmentDate) - new Date(b.appointmentDate)
            )[0];
            patient.nextDueAppointment = nextAppointment.appointmentDate;
        }

        await patient.save();

        res.status(201).json({
            success: true,
            message: 'Appointment scheduled successfully',
            patient
        });
    } catch (error) {
        res.status(500).json({
            success: false,
            message: 'Server error scheduling appointment',
            error: error.message
        });
    }
};

// @desc    Update appointment status
// @route   PUT /api/patients/:id/appointments/:appointmentId/status
// @access  Private/Admin/Employee
const updateAppointmentStatus = async (req, res) => {
    try {
        const { status, notes } = req.body;

        if (!['Scheduled', 'Completed', 'Cancelled'].includes(status)) {
            return res.status(400).json({
                success: false,
                message: 'Invalid appointment status'
            });
        }

        const patient = await Patient.findById(req.params.id);
        if (!patient) {
            return res.status(404).json({
                success: false,
                message: 'Patient not found'
            });
        }

        const appointment = patient.appointmentDetails.id(req.params.appointmentId);
        if (!appointment) {
            return res.status(404).json({
                success: false,
                message: 'Appointment not found'
            });
        }

        appointment.status = status;
        if (notes !== undefined) appointment.notes = notes;

        // Update next due appointment
        const futureAppointments = patient.appointmentDetails.filter(
            app => new Date(app.appointmentDate) > new Date() && app.status === 'Scheduled'
        );
        
        if (futureAppointments.length > 0) {
            const nextAppointment = futureAppointments.sort(
                (a, b) => new Date(a.appointmentDate) - new Date(b.appointmentDate)
            )[0];
            patient.nextDueAppointment = nextAppointment.appointmentDate;
        } else {
            patient.nextDueAppointment = null;
        }

        await patient.save();

        res.json({
            success: true,
            message: 'Appointment status updated successfully',
            patient
        });
    } catch (error) {
        res.status(500).json({
            success: false,
            message: 'Server error updating appointment status',
            error: error.message
        });
    }
};

// @desc    Get patients with upcoming appointments
// @route   GET /api/patients/upcoming-appointments
// @access  Private/Admin/Employee
const getPatientsWithUpcomingAppointments = async (req, res) => {
    try {
        const { days = 7, limit = 20 } = req.query;
        
        const currentDate = new Date();
        const futureDate = new Date();
        futureDate.setDate(currentDate.getDate() + parseInt(days));

        const patients = await Patient.find({
            nextDueAppointment: {
                $gte: currentDate,
                $lte: futureDate
            }
        })
        .sort({ nextDueAppointment: 1 })
        .limit(parseInt(limit))
        .select('name contactNumber nextDueAppointment appointmentDetails');

        res.json({
            success: true,
            patients,
            count: patients.length
        });
    } catch (error) {
        res.status(500).json({
            success: false,
            message: 'Server error fetching upcoming appointments',
            error: error.message
        });
    }
};

// @desc    Get patient statistics
// @route   GET /api/patients/stats
// @access  Private/Admin/Employee
const getPatientStats = async (req, res) => {
    try {
        const totalPatients = await Patient.countDocuments();
        const malePatients = await Patient.countDocuments({ gender: 'Male' });
        const femalePatients = await Patient.countDocuments({ gender: 'Female' });
        const otherGenderPatients = await Patient.countDocuments({ gender: 'Other' });

        // Age distribution
        const ageDistribution = await Patient.aggregate([
            {
                $group: {
                    _id: {
                        $switch: {
                            branches: [
                                { case: { $lt: ['$age', 18] }, then: 'Under 18' },
                                { case: { $lt: ['$age', 30] }, then: '18-29' },
                                { case: { $lt: ['$age', 50] }, then: '30-49' },
                                { case: { $lt: ['$age', 65] }, then: '50-64' },
                                { case: { $gte: ['$age', 65] }, then: '65+' }
                            ]
                        }
                    },
                    count: { $sum: 1 }
                }
            },
            { $sort: { '_id': 1 } }
        ]);

        // Upcoming appointments
        const upcomingAppointments = await Patient.countDocuments({
            nextDueAppointment: { $gte: new Date() }
        });

        // Patients with health conditions
        const patientsWithConditions = await Patient.countDocuments({
            'healthConditions.0': { $exists: true }
        });

        // Recent patients (last 30 days)
        const thirtyDaysAgo = new Date();
        thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);
        const recentPatients = await Patient.countDocuments({
            createdAt: { $gte: thirtyDaysAgo }
        });

        // Most common health conditions
        const commonConditions = await Patient.aggregate([
            { $unwind: '$healthConditions' },
            {
                $group: {
                    _id: '$healthConditions.condition',
                    count: { $sum: 1 }
                }
            },
            { $sort: { count: -1 } },
            { $limit: 10 }
        ]);

        res.json({
            success: true,
            stats: {
                totalPatients,
                genderDistribution: {
                    male: malePatients,
                    female: femalePatients,
                    other: otherGenderPatients
                },
                ageDistribution,
                upcomingAppointments,
                patientsWithConditions,
                recentPatients,
                commonConditions
            }
        });
    } catch (error) {
        res.status(500).json({
            success: false,
            message: 'Server error fetching patient statistics',
            error: error.message
        });
    }
};

module.exports = {
    createPatient,
    getPatients,
    getPatientById,
    updatePatient,
    deletePatient,
    addHealthCondition,
    updateHealthCondition,
    deleteHealthCondition,
    scheduleAppointment,
    updateAppointmentStatus,
    getPatientsWithUpcomingAppointments,
    getPatientStats
};
