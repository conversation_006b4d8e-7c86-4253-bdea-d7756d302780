const http = require('http');
const mongoose = require('mongoose');
const User = require('./models/User');
require('dotenv').config();

function makeRequest(options, data) {
    return new Promise((resolve, reject) => {
        const req = http.request(options, (res) => {
            let body = '';
            res.on('data', (chunk) => {
                body += chunk;
            });
            res.on('end', () => {
                try {
                    const jsonBody = JSON.parse(body);
                    resolve({ status: res.statusCode, data: jsonBody });
                } catch (e) {
                    resolve({ status: res.statusCode, data: body });
                }
            });
        });

        req.on('error', (err) => {
            reject(err);
        });

        if (data) {
            req.write(JSON.stringify(data));
        }
        req.end();
    });
}

async function getTwoFactorCode(userId) {
    await mongoose.connect(process.env.DB_URL, {
        dbName: process.env.DB_NAME || 'ecommerce',
    });
    
    const user = await User.findById(userId);
    const code = user ? user.twoFactorCode : null;
    
    await mongoose.connection.close();
    return code;
}

async function testCompleteFlow() {
    console.log('🧪 Testing Complete Authentication Flow...\n');

    try {
        // Step 1: Admin Login
        console.log('1. Testing Admin Login...');
        const loginOptions = {
            hostname: 'localhost',
            port: 5000,
            path: '/api/auth/login-admin',
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            }
        };

        const loginData = {
            email: '<EMAIL>',
            password: 'Admin123!'
        };

        const loginResponse = await makeRequest(loginOptions, loginData);
        console.log('✅ Login Status:', loginResponse.status);
        console.log('📝 Login Response:', loginResponse.data);

        if (loginResponse.data.success && loginResponse.data.userId) {
            // Step 2: Get the two-factor code from database
            console.log('\n2. Getting two-factor code from database...');
            const twoFactorCode = await getTwoFactorCode(loginResponse.data.userId);
            console.log('🔐 Two-Factor Code:', twoFactorCode);

            if (twoFactorCode) {
                // Step 3: Verify two-factor code
                console.log('\n3. Testing Two-Factor Verification...');
                const verifyOptions = {
                    hostname: 'localhost',
                    port: 5000,
                    path: '/api/auth/verify-code',
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    }
                };

                const verifyData = {
                    userId: loginResponse.data.userId,
                    code: twoFactorCode
                };

                const verifyResponse = await makeRequest(verifyOptions, verifyData);
                console.log('✅ Verify Status:', verifyResponse.status);
                console.log('📝 Verify Response:', verifyResponse.data);

                if (verifyResponse.data.success && verifyResponse.data.token) {
                    console.log('\n🎉 COMPLETE AUTHENTICATION FLOW SUCCESSFUL!');
                    console.log('🎟️  JWT Token:', verifyResponse.data.token);
                    console.log('👤 User Info:', verifyResponse.data.user);
                    
                    // Step 4: Test protected endpoint with JWT token
                    console.log('\n4. Testing protected endpoint with JWT token...');
                    const protectedOptions = {
                        hostname: 'localhost',
                        port: 5000,
                        path: '/api/user/users/profile',
                        method: 'GET',
                        headers: {
                            'Authorization': `Bearer ${verifyResponse.data.token}`,
                            'Content-Type': 'application/json',
                        }
                    };

                    const protectedResponse = await makeRequest(protectedOptions);
                    console.log('✅ Protected endpoint status:', protectedResponse.status);
                    console.log('📝 Protected endpoint response:', protectedResponse.data);
                } else {
                    console.log('❌ Two-factor verification failed');
                }
            } else {
                console.log('❌ No two-factor code found');
            }
        } else {
            console.log('❌ Admin login failed');
        }

    } catch (error) {
        console.error('❌ Error:', error.message);
    }
}

testCompleteFlow();
