require("dotenv").config();
const mongoose = require("mongoose");
const bcrypt = require("bcryptjs");
const User = require("./models/User");

async function createTestEmployee() {
  try {
    // Connect to MongoDB
    await mongoose.connect(process.env.DB_URL + process.env.DB_NAME);
    console.log("✅ Connected to MongoDB");

    // Delete existing test employee
    await User.deleteOne({ email: "<EMAIL>" });
    console.log("🗑️ Deleted existing test employee");

    // Hash password
    const salt = await bcrypt.genSalt(10);
    const hashedPassword = await bcrypt.hash("Employee123!", salt);

    // Create new employee user
    const employeeUser = new User({
      name: "Test Employee",
      email: "<EMAIL>",
      password: hashedPassword,
      phone: "9876543211",
      role: "employee",
      department: "Laboratory",
      status: "active",
      isEmailVerified: true,
      isPasswordSet: true,
      paymentMethod: "card",
      joinedDate: Date.now(),
    });

    await employeeUser.save();
    console.log("✅ Test employee user created successfully!");
    console.log("📧 Email: <EMAIL>");
    console.log("🔑 Password: Employee123!");
    console.log("👤 Role: employee");
    console.log("🏢 Department: Laboratory");
    console.log("🔐 Password Set: true");

  } catch (error) {
    console.error("❌ Error:", error);
  } finally {
    await mongoose.disconnect();
    console.log("🔌 Disconnected from MongoDB");
  }
}

createTestEmployee();
