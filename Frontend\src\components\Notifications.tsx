import { useState, useEffect } from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>it<PERSON> } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import {
  Bell,
  AlertCircle,
  FileText,
  BarChart3,
  Calendar,
  CheckCircle,
  Trash2,
  AlertTriangle,
  XCircle,
  Package,
  User,
  Settings,
  RefreshCw,
} from "lucide-react";
import { useToast } from "@/hooks/use-toast";
import notificationsAPI from "@/api/notifications.api.js";
import { apiHelpers } from "@/utils/apiHelpers";

// Icon mapping for dynamic icon rendering
const iconMap = {
  AlertCircle,
  AlertTriangle,
  CheckCircle,
  Bell,
  BarChart3,
  FileText,
  Calendar,
  Package,
  User,
  Settings,
  XCircle,
};

const Notifications = () => {
  const { toast } = useToast();
  const [notifications, setNotifications] = useState([]);
  const [categories, setCategories] = useState([]);
  const [activeCategory, setActiveCategory] = useState("all");
  const [loading, setLoading] = useState(true);
  const [isRefreshing, setIsRefreshing] = useState(false);

  // Fetch notifications from API
  const fetchNotifications = async (showLoading = true) => {
    try {
      if (showLoading) setLoading(true);

      const filters = {
        limit: 100, // Get all notifications
        sortBy: "createdAt",
        sortOrder: "desc",
      };

      const response = await notificationsAPI.getNotifications(filters);

      if (apiHelpers.isSuccessResponse(response)) {
        const data = apiHelpers.extractData(response);
        const notificationsData = data.notifications || [];

        // Format notifications for display
        const formattedNotifications = notificationsData.map((notification) =>
          notificationsAPI.formatNotificationForDisplay(notification)
        );

        setNotifications(formattedNotifications);

        // Update categories with counts
        const notificationCategories =
          notificationsAPI.getNotificationCategories(formattedNotifications);
        setCategories(notificationCategories);

        console.log("Notifications data loaded:", formattedNotifications);
      } else {
        throw new Error(response.message || "Failed to fetch notifications");
      }
    } catch (error) {
      console.error("Failed to fetch notifications:", error);
      toast({
        title: "Error",
        description: apiHelpers.formatError(error),
        variant: "destructive",
      });

      // Set empty state
      setNotifications([]);
      setCategories([
        { title: "All", count: 0, active: true, filter: "all" },
        { title: "Alerts", count: 0, active: false, filter: "alerts" },
        { title: "Reports", count: 0, active: false, filter: "reports" },
        { title: "Updates", count: 0, active: false, filter: "updates" },
        { title: "Unread", count: 0, active: false, filter: "unread" },
      ]);
    } finally {
      setLoading(false);
      setIsRefreshing(false);
    }
  };

  // Load notifications on component mount
  useEffect(() => {
    fetchNotifications();
  }, []);

  // Refresh notifications
  const handleRefresh = async () => {
    setIsRefreshing(true);
    await fetchNotifications(false);
  };

  // Filter notifications by active category
  const filteredNotifications = notificationsAPI.filterNotificationsByCategory(
    notifications,
    activeCategory
  );

  // Mark notification as read
  const handleMarkAsRead = async (id) => {
    try {
      await notificationsAPI.markNotificationRead(id);
      setNotifications((prev) =>
        prev.map((n) => (n.id === id ? { ...n, read: true } : n))
      );
      toast({
        title: "Marked as Read",
        description: "Notification has been marked as read",
      });
    } catch (error) {
      console.error("Failed to mark notification as read:", error);
      toast({
        title: "Error",
        description: "Failed to mark notification as read",
        variant: "destructive",
      });
    }
  };

  // Delete notification (if supported by backend)
  const handleDeleteNotification = (id) => {
    setNotifications((prev) => prev.filter((n) => n.id !== id));
    toast({
      title: "Notification Removed",
      description: "Notification has been removed from view",
    });
  };

  // Mark all notifications as read
  const handleMarkAllAsRead = async () => {
    try {
      await notificationsAPI.markAllNotificationsRead();
      setNotifications((prev) => prev.map((n) => ({ ...n, read: true })));
      toast({
        title: "All Marked as Read",
        description: "All notifications have been marked as read",
      });
    } catch (error) {
      console.error("Failed to mark all notifications as read:", error);
      toast({
        title: "Error",
        description: "Failed to mark all notifications as read",
        variant: "destructive",
      });
    }
  };

  const unreadCount = notifications.filter((n) => !n.read).length;

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case "high":
        return "text-red-600";
      case "medium":
        return "text-yellow-600";
      case "low":
        return "text-green-600";
      default:
        return "text-gray-600";
    }
  };

  const getTypeColor = (type: string) => {
    switch (type) {
      case "alert":
        return "bg-red-100 text-red-700";
      case "report":
        return "bg-blue-100 text-blue-700";
      case "update":
        return "bg-green-100 text-green-700";
      default:
        return "bg-gray-100 text-gray-700";
    }
  };

  return (
    <div className="space-y-6 transition-colors duration-500 ease-in-out">
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-3">
          <Bell className="w-6 h-6 text-gray-600 dark:text-gray-300 transition-colors duration-500 ease-in-out" />
          <div>
            <h1 className="text-3xl font-bold text-gray-900 dark:text-white transition-colors duration-500 ease-in-out">
              Notifications
            </h1>
            <p className="text-lg text-gray-600 dark:text-gray-300 transition-colors duration-500 ease-in-out">
              Stay updated with important alerts and reports
            </p>
          </div>
        </div>
        <div className="flex items-center gap-3">
          <Badge variant="secondary" className="bg-red-100 text-red-700">
            {unreadCount} Unread
          </Badge>
          <Button
            variant="outline"
            onClick={handleRefresh}
            disabled={isRefreshing}
            className="transition-colors duration-300 ease-in-out"
          >
            <RefreshCw
              className={`w-4 h-4 mr-2 ${isRefreshing ? "animate-spin" : ""}`}
            />
            Refresh
          </Button>
          <Button
            variant="outline"
            onClick={handleMarkAllAsRead}
            disabled={loading || notifications.length === 0}
            className="transition-colors duration-300 ease-in-out"
          >
            Mark All Read
          </Button>
        </div>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-4 gap-6">
        {/* Categories Sidebar */}
        <Card className="lg:col-span-1 transition-colors duration-500 ease-in-out bg-white dark:bg-gray-800 border-gray-200 dark:border-gray-700">
          <CardHeader>
            <CardTitle className="text-2xl text-gray-900 dark:text-white transition-colors duration-500 ease-in-out">
              Categories
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-2">
              {categories.map((category) => (
                <button
                  key={category.filter}
                  onClick={() => setActiveCategory(category.filter)}
                  className={`w-full flex items-center justify-between p-3 rounded-lg text-left transition-colors duration-300 ease-in-out ${
                    activeCategory === category.filter
                      ? "bg-blue-50 dark:bg-blue-900 text-blue-700 dark:text-blue-300 border border-blue-200 dark:border-blue-700"
                      : "hover:bg-gray-50 dark:hover:bg-gray-700 text-gray-700 dark:text-gray-300"
                  }`}
                >
                  <span className="font-medium">{category.title}</span>
                  <Badge variant="secondary" className="text-xs">
                    {category.count}
                  </Badge>
                </button>
              ))}
            </div>
          </CardContent>
        </Card>

        {/* Notifications List */}
        <Card className="lg:col-span-3 transition-colors duration-500 ease-in-out bg-white dark:bg-gray-800 border-gray-200 dark:border-gray-700">
          <CardHeader>
            <CardTitle className="text-gray-900 dark:text-white transition-colors duration-500 ease-in-out">
              {activeCategory} Notifications
              <span className="text-sm font-normal text-gray-500 dark:text-gray-400 ml-2 transition-colors duration-500 ease-in-out">
                ({filteredNotifications.length})
              </span>
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {filteredNotifications.map((notification) => (
                <div
                  key={notification.id}
                  className={`flex items-start gap-4 p-4 rounded-lg border transition-all duration-500 ease-in-out animate-fade-in ${
                    notification.read
                      ? "bg-gray-50 dark:bg-gray-700 border-gray-200 dark:border-gray-600"
                      : "bg-white dark:bg-gray-800 border-blue-200 dark:border-blue-700 shadow-sm"
                  }`}
                >
                  <div
                    className={`p-2 rounded-lg transition-colors duration-300 ease-in-out ${getTypeColor(
                      notification.type
                    )}`}
                  >
                    {(() => {
                      const IconComponent = iconMap[notification.icon] || Bell;
                      return <IconComponent className="w-5 h-5" />;
                    })()}
                  </div>

                  <div className="flex-1">
                    <div className="flex items-start justify-between">
                      <div>
                        <h3
                          className={`font-medium transition-colors duration-500 ease-in-out ${
                            notification.read
                              ? "text-gray-700 dark:text-gray-300"
                              : "text-gray-900 dark:text-white"
                          }`}
                        >
                          {notification.title}
                        </h3>
                        <p
                          className={`text-sm mt-1 transition-colors duration-500 ease-in-out ${
                            notification.read
                              ? "text-gray-500 dark:text-gray-400"
                              : "text-gray-600 dark:text-gray-300"
                          }`}
                        >
                          {notification.message}
                        </p>
                        <div className="flex items-center gap-3 mt-2">
                          <span className="text-xs text-gray-500 dark:text-gray-400 transition-colors duration-500 ease-in-out">
                            {notification.time}
                          </span>
                          <Badge
                            variant="outline"
                            className={`text-xs ${getPriorityColor(
                              notification.priority
                            )}`}
                          >
                            {notification.priority}
                          </Badge>
                          <Badge
                            variant="secondary"
                            className={`text-xs ${getTypeColor(
                              notification.type
                            )}`}
                          >
                            {notification.type}
                          </Badge>
                          <Badge variant="outline" className="text-xs">
                            {notification.type}
                          </Badge>
                        </div>
                      </div>

                      <div className="flex items-center gap-2 ml-4">
                        {!notification.read && (
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => handleMarkAsRead(notification.id)}
                            className="text-blue-600 hover:text-blue-700 transition-colors duration-300 ease-in-out"
                          >
                            <CheckCircle className="w-4 h-4" />
                          </Button>
                        )}
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() =>
                            handleDeleteNotification(notification.id)
                          }
                          className="text-red-600 hover:text-red-700 transition-colors duration-300 ease-in-out"
                        >
                          <Trash2 className="w-4 h-4" />
                        </Button>
                      </div>
                    </div>
                  </div>
                </div>
              ))}

              {filteredNotifications.length === 0 && (
                <div className="text-center py-12">
                  <Bell className="w-12 h-12 text-gray-300 dark:text-gray-600 mx-auto mb-4 transition-colors duration-500 ease-in-out" />
                  <p className="text-gray-500 dark:text-gray-400 transition-colors duration-500 ease-in-out">
                    No notifications in this category
                  </p>
                </div>
              )}
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
};

export default Notifications;
