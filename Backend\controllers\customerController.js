const User = require('../models/User');
const Order = require('../models/Order');

// @desc    Get all customers
// @route   GET /api/admin/customers
// @access  Private/Admin
const getAllCustomers = async (req, res) => {
    try {
        const { page = 1, limit = 10, search, status } = req.query;

        // Build query object
        const query = { role: 'user' }; // Only get users with 'user' role (customers)
        
        if (search) {
            query.$or = [
                { name: { $regex: search, $options: 'i' } },
                { email: { $regex: search, $options: 'i' } },
                { phone: { $regex: search, $options: 'i' } }
            ];
        }

        if (status) {
            query.isActive = status === 'active';
        }

        const customers = await User.find(query)
            .select('-password -verificationToken -resetPasswordToken')
            .sort({ createdAt: -1 })
            .limit(limit * 1)
            .skip((page - 1) * limit);

        const total = await User.countDocuments(query);

        res.json({
            success: true,
            customers,
            pagination: {
                page: parseInt(page),
                limit: parseInt(limit),
                total,
                pages: Math.ceil(total / limit)
            }
        });
    } catch (error) {
        console.error('Get customers error:', error);
        res.status(500).json({
            success: false,
            message: 'Failed to fetch customers'
        });
    }
};

// @desc    Get customer by ID
// @route   GET /api/admin/customers/:id
// @access  Private/Admin
const getCustomerById = async (req, res) => {
    try {
        const customer = await User.findById(req.params.id)
            .select('-password -verificationToken -resetPasswordToken');

        if (!customer) {
            return res.status(404).json({
                success: false,
                message: 'Customer not found'
            });
        }

        res.json({
            success: true,
            customer
        });
    } catch (error) {
        console.error('Get customer by ID error:', error);
        res.status(500).json({
            success: false,
            message: 'Failed to fetch customer'
        });
    }
};

// @desc    Update customer
// @route   PUT /api/admin/customers/:id
// @access  Private/Admin
const updateCustomer = async (req, res) => {
    try {
        const { name, email, phone, isActive } = req.body;

        const customer = await User.findById(req.params.id);

        if (!customer) {
            return res.status(404).json({
                success: false,
                message: 'Customer not found'
            });
        }

        // Update fields
        if (name) customer.name = name;
        if (email) customer.email = email;
        if (phone) customer.phone = phone;
        if (typeof isActive !== 'undefined') customer.isActive = isActive;

        await customer.save();

        res.json({
            success: true,
            message: 'Customer updated successfully',
            customer: {
                _id: customer._id,
                name: customer.name,
                email: customer.email,
                phone: customer.phone,
                isActive: customer.isActive,
                createdAt: customer.createdAt
            }
        });
    } catch (error) {
        console.error('Update customer error:', error);
        res.status(500).json({
            success: false,
            message: 'Failed to update customer'
        });
    }
};

// @desc    Delete customer
// @route   DELETE /api/admin/customers/:id
// @access  Private/Admin
const deleteCustomer = async (req, res) => {
    try {
        const customer = await User.findById(req.params.id);

        if (!customer) {
            return res.status(404).json({
                success: false,
                message: 'Customer not found'
            });
        }

        await User.findByIdAndDelete(req.params.id);

        res.json({
            success: true,
            message: 'Customer deleted successfully'
        });
    } catch (error) {
        console.error('Delete customer error:', error);
        res.status(500).json({
            success: false,
            message: 'Failed to delete customer'
        });
    }
};

// @desc    Get customer statistics
// @route   GET /api/admin/customers/stats
// @access  Private/Admin
const getCustomerStats = async (req, res) => {
    try {
        const totalCustomers = await User.countDocuments({ role: 'user' });
        const activeCustomers = await User.countDocuments({ role: 'user', isActive: true });
        const inactiveCustomers = await User.countDocuments({ role: 'user', isActive: false });

        // Get customers registered this month
        const startOfMonth = new Date();
        startOfMonth.setDate(1);
        startOfMonth.setHours(0, 0, 0, 0);

        const newThisMonth = await User.countDocuments({
            role: 'user',
            createdAt: { $gte: startOfMonth }
        });

        res.json({
            success: true,
            stats: {
                total: totalCustomers,
                active: activeCustomers,
                inactive: inactiveCustomers,
                newThisMonth
            }
        });
    } catch (error) {
        console.error('Get customer stats error:', error);
        res.status(500).json({
            success: false,
            message: 'Failed to fetch customer statistics'
        });
    }
};

// @desc    Get customer orders
// @route   GET /api/admin/customers/:id/orders
// @access  Private/Admin
const getCustomerOrders = async (req, res) => {
    try {
        const { page = 1, limit = 10 } = req.query;

        const orders = await Order.find({ user: req.params.id })
            .populate('items.product', 'name price')
            .sort({ createdAt: -1 })
            .limit(limit * 1)
            .skip((page - 1) * limit);

        const total = await Order.countDocuments({ user: req.params.id });

        res.json({
            success: true,
            orders,
            pagination: {
                page: parseInt(page),
                limit: parseInt(limit),
                total,
                pages: Math.ceil(total / limit)
            }
        });
    } catch (error) {
        console.error('Get customer orders error:', error);
        res.status(500).json({
            success: false,
            message: 'Failed to fetch customer orders'
        });
    }
};

module.exports = {
    getAllCustomers,
    getCustomerById,
    updateCustomer,
    deleteCustomer,
    getCustomerStats,
    getCustomerOrders
};
