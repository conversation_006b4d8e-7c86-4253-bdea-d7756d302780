# 🏥 Dr<PERSON> - Admin Panel

A comprehensive admin panel for Dr. <PERSON> Laboratories built with modern web technologies. This system provides complete management capabilities for laboratory operations, staff management, patient records, inventory, orders, and more.

## 🚀 Features

### 🔐 Authentication & Security

- **Multi-Role Authentication**: Admin and Employee login with role-based access control
- **Two-Factor Authentication (2FA)**: Email-based verification for enhanced security
- **JWT Token Management**: Secure token-based authentication with 3-day expiry
- **"Keep Me Logged In"**: Persistent login option with localStorage/sessionStorage management
- **Password Reset**: Secure password reset flow with branded email notifications

### 👥 Staff Management

- **Staff Invitation System**: Send professional branded invitation emails to new staff members
- **Role-Based Access**: Admin and Employee roles with appropriate permissions
- **Complete Staff Profiles**: Manage staff information, departments, positions, and status
- **Real-time Updates**: Live staff data with instant table updates

### 📊 Dashboard & Analytics

- **Comprehensive Dashboard**: Overview of all laboratory operations
- **Real-time Data**: Live updates from database without dummy data
- **Analytics & Reports**: Detailed insights into laboratory performance
- **Empty State Handling**: Clean empty states when no data exists

### 🧪 Laboratory Management

- **Patient Records**: Complete patient management system
- **Test Results**: Laboratory test management and reporting
- **Inventory Management**: Track laboratory supplies and equipment
- **Order Processing**: Handle laboratory test orders and requests

### 📧 Email System

- **Branded Email Templates**: Professional emails with Dr. Kumar logo and branding
- **Staff Invitations**: Automated invitation emails for new staff members
- **Password Reset Emails**: Secure password reset notifications
- **Welcome Messages**: Branded welcome emails for new users

## 🛠️ Tech Stack

### Frontend

- **React 18** with TypeScript
- **Vite** for fast development and building
- **Tailwind CSS** for styling
- **Radix UI** for accessible components
- **React Router** for navigation
- **React Hook Form** for form management
- **Lucide React** for icons

### Backend

- **Node.js** with Express.js
- **MongoDB** with Mongoose ODM
- **JWT** for authentication
- **Bcrypt** for password hashing
- **Nodemailer** for email functionality
- **Multer** for file uploads
- **CORS** for cross-origin requests

## 📁 Project Structure

```
Dr-Kumar-Admin/
├── Frontend/                 # React TypeScript frontend
│   ├── src/
│   │   ├── components/      # React components
│   │   ├── api/            # API integration layer
│   │   ├── contexts/       # React contexts
│   │   ├── hooks/          # Custom React hooks
│   │   ├── services/       # Business logic services
│   │   └── utils/          # Utility functions
│   ├── public/             # Static assets
│   └── package.json        # Frontend dependencies
├── Backend/                 # Node.js Express backend
│   ├── controllers/        # Route controllers
│   ├── models/            # MongoDB models
│   ├── routes/            # API routes
│   ├── middleware/        # Custom middleware
│   ├── utils/             # Utility functions
│   ├── config/            # Configuration files
│   └── package.json       # Backend dependencies
└── README.md              # Project documentation
```

## 🚀 Quick Start

### Prerequisites

- Node.js (v16 or higher)
- MongoDB (local or cloud instance)
- Gmail account for email functionality

### 1. Clone the Repository

```bash
git clone https://github.com/CodehubPriyanshu/Dr.Kumar-Admin.git
cd Dr.Kumar-Admin
```

### 2. Backend Setup

```bash
cd Backend
npm install
```

Create a `.env` file in the Backend directory:

```env
# Server
PORT=5000

# Database
DB_URL=your_mongodb_connection_string
DB_NAME=ecommerce

# JWT
JWT_SECRET=your_jwt_secret_key

# Email Configuration
EMAIL_HOST=smtp.gmail.com
EMAIL_PORT=587
EMAIL_USER=your_gmail_address
EMAIL_PASS=your_gmail_app_password

# Frontend URL
FRONTEND_URL=http://localhost:8081
```

Start the backend server:

```bash
npm start
```

### 3. Frontend Setup

```bash
cd Frontend
npm install
npm run dev
```

### 4. Create Admin User

Run the admin setup script:

```bash
cd Backend
node fix-admin-user.js
```

## 🔑 Default Login Credentials

### Admin Access

- **Email**: `<EMAIL>`
- **Password**: `Admin123!`
- **Role**: Administrator

_Note: Two-factor authentication is enabled. Check your email for the verification code._

## 📱 Application URLs

- **Frontend**: http://localhost:8081
- **Backend API**: http://localhost:5000
- **API Health Check**: http://localhost:5000/api/health

## 🎯 Key Features Implemented

### ✅ Complete Authentication System

- Multi-role login (Admin/Employee)
- Two-factor authentication
- JWT token management
- Password reset functionality
- "Keep me logged in" feature

### ✅ Staff Management System

- Professional staff invitation emails
- Role-based access control
- Complete staff profiles
- Real-time staff data management

### ✅ Email Integration

- Branded email templates
- SMTP configuration with Gmail
- Automated invitation emails
- Password reset notifications

### ✅ Database Integration

- MongoDB with Mongoose
- Real-time data updates
- Proper validation and error handling
- Clean empty states

### ✅ Security Features

- JWT authentication
- Password hashing with bcrypt
- Role-based route protection
- Input validation and sanitization

## 🧪 Testing

### Backend Testing

```bash
cd Backend
node test-staff-invitation.js    # Test staff invitation flow
node test-email-sending.js       # Test email functionality
```

### API Testing

- Import the Postman collection: `Backend/drkumar_full_api.postman_collection.json`
- Test all API endpoints with proper authentication

## 🔧 Development

### Frontend Development

```bash
cd Frontend
npm run dev          # Start development server
npm run build        # Build for production
npm run lint         # Run ESLint
```

### Backend Development

```bash
cd Backend
npm run dev          # Start with nodemon (auto-restart)
npm start           # Start production server
```

## 📧 Email Configuration

The system uses Gmail SMTP for sending emails. To set up:

1. Enable 2-factor authentication on your Gmail account
2. Generate an App Password for the application
3. Use the App Password in the `EMAIL_PASS` environment variable

## 🛡️ Security Considerations

- JWT tokens expire after 3 days
- Passwords are hashed using bcrypt
- Two-factor authentication is mandatory
- Role-based access control is enforced
- Input validation on all endpoints
- CORS configuration for security

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Test thoroughly
5. Submit a pull request

## 📄 License

This project is licensed under the ISC License.

## 👨‍💻 Author

**Priyanshu**

- GitHub: [@CodehubPriyanshu](https://github.com/CodehubPriyanshu)

## 🆘 Support

For support and questions:

- Create an issue on GitHub
- Contact: <EMAIL>

---

**Dr. Kumar Laboratories Admin Panel** - Streamlining laboratory management with modern technology. 🏥✨
