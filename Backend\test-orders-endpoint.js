const axios = require('axios');

const BASE_URL = 'http://localhost:5000';

async function testOrdersEndpoint() {
    console.log('🧪 Testing Orders Endpoint...\n');

    try {
        // Test 1: Check if orders endpoint exists (without auth)
        console.log('1. Testing orders endpoint without authentication...');
        try {
            const response = await axios.get(`${BASE_URL}/api/admin/orders`);
            console.log('❌ Orders endpoint should require authentication');
        } catch (error) {
            if (error.response && error.response.status === 401) {
                console.log('✅ Orders endpoint properly requires authentication');
                console.log('   Status:', error.response.status);
                console.log('   Message:', error.response.data?.message);
            } else {
                console.log('❓ Unexpected error:', error.message);
            }
        }

        // Test 2: Test with mock token to see endpoint structure
        console.log('\n2. Testing orders endpoint structure...');
        try {
            const response = await axios.get(`${BASE_URL}/api/admin/orders`, {
                headers: {
                    'Authorization': 'Bearer mock-token'
                }
            });
            console.log('✅ Orders endpoint response:', response.data);
        } catch (error) {
            if (error.response) {
                console.log('📋 Orders endpoint error (expected due to auth):');
                console.log('   Status:', error.response.status);
                console.log('   Message:', error.response.data?.message);
                
                // Check if it's a token validation error (good) vs route not found (bad)
                if (error.response.status === 401 || error.response.status === 403) {
                    console.log('✅ Endpoint exists and is properly protected');
                } else if (error.response.status === 404) {
                    console.log('❌ Endpoint not found - route may not be configured');
                }
            } else {
                console.log('❌ Network error:', error.message);
            }
        }

        // Test 3: Check if the route is registered
        console.log('\n3. Testing if admin routes are accessible...');
        try {
            const response = await axios.get(`${BASE_URL}/api/admin/`);
            console.log('✅ Admin routes are accessible');
        } catch (error) {
            if (error.response && error.response.status === 404) {
                console.log('❌ Admin routes not found - check route configuration');
            } else {
                console.log('📋 Admin routes response:', error.response?.status, error.response?.data?.message);
            }
        }

        // Test 4: Test basic server health
        console.log('\n4. Testing server health...');
        try {
            const response = await axios.get(`${BASE_URL}/`);
            console.log('✅ Server is responding');
            console.log('   Response:', response.data);
        } catch (error) {
            console.log('❌ Server health check failed:', error.message);
        }

        console.log('\n💡 Endpoint testing completed');

    } catch (error) {
        console.error('❌ Test failed:', error.message);
    }
}

// Run the test
testOrdersEndpoint();
