const Product = require("../models/Products");
const User = require("../models/User");

// @desc    Get all products
// @route   GET /api/products
// @access  Public
const getProducts = async (req, res) => {
  try {
    const {
      page = 1,
      limit = 10,
      category,
      categoryId,
      minPrice,
      maxPrice,
      featured,
      inStock,
      search,
      sortBy = "createdAt",
      sortOrder = "desc",
    } = req.query;

    // Build query object
    const query = { active: true };

    if (category) query.category = { $regex: category, $options: "i" };
    if (categoryId) query.categoryId = categoryId;
    if (minPrice || maxPrice) {
      query.price = {};
      if (minPrice) query.price.$gte = parseFloat(minPrice);
      if (maxPrice) query.price.$lte = parseFloat(maxPrice);
    }
    if (featured !== undefined) query.isFeatured = featured === "true";
    if (inStock !== undefined) query.inStock = inStock === "true";
    if (search) {
      query.$or = [
        { name: { $regex: search, $options: "i" } },
        { description: { $regex: search, $options: "i" } },
      ];
    }

    // Build sort object
    const sort = {};
    sort[sortBy] = sortOrder === "desc" ? -1 : 1;

    const products = await Product.find(query)
      .populate("categoryId", "name")
      .sort(sort)
      .limit(limit * 1)
      .skip((page - 1) * limit);

    const total = await Product.countDocuments(query);

    res.json({
      success: true,
      data: {
        products,
        pagination: {
          page: parseInt(page),
          limit: parseInt(limit),
          total,
          pages: Math.ceil(total / limit),
        },
      },
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: "Server error fetching products",
      error: error.message,
    });
  }
};

// @desc    Get product by ID
// @route   GET /api/products/:id
// @access  Public
const getProductById = async (req, res) => {
  try {
    const product = await Product.findById(req.params.id)
      .populate("categoryId", "name")
      .populate("reviews.user", "name");

    if (!product) {
      return res.status(404).json({
        success: false,
        message: "Product not found",
      });
    }

    res.json({
      success: true,
      data: { product },
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: "Server error fetching product",
      error: error.message,
    });
  }
};

// @desc    Create new product
// @route   POST /api/products
// @access  Private/Admin
const createProduct = async (req, res) => {
  try {
    const {
      name,
      description,
      categoryId,
      price,
      category,
      inStock,
      isFeatured,
    } = req.body;
    let images = [];
    if (req.files && req.files.length > 0) {
      images = req.files.map((file) => ({ url: `/uploads/${file.filename}` }));
    } else if (req.body.images) {
      images = req.body.images;
    }
    if (!name || !categoryId || !price) {
      return res.status(400).json({
        success: false,
        message: "Name, category ID, and price are required",
      });
    }
    const product = new Product({
      name,
      description,
      categoryId,
      price,
      category,
      inStock: inStock !== undefined ? inStock : true,
      images,
      isFeatured: isFeatured || false,
    });

    await product.save();

    const populatedProduct = await Product.findById(product._id).populate(
      "categoryId",
      "name"
    );

    res.status(201).json({
      success: true,
      message: "Product created successfully",
      data: { product: populatedProduct },
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: "Server error creating product",
      error: error.message,
    });
  }
};

// @desc    Update product
// @route   PUT /api/products/:id
// @access  Private/Admin
const updateProduct = async (req, res) => {
  try {
    const {
      name,
      description,
      categoryId,
      price,
      category,
      inStock,
      isFeatured,
      active,
    } = req.body;
    const product = await Product.findById(req.params.id);
    if (!product) {
      return res.status(404).json({
        success: false,
        message: "Product not found",
      });
    }
    if (req.files && req.files.length > 0) {
      product.images = req.files.map((file) => ({
        url: `/uploads/${file.filename}`,
      }));
    } else if (req.body.images !== undefined) {
      product.images = req.body.images;
    }

    // Update fields if provided
    if (name !== undefined) product.name = name;
    if (description !== undefined) product.description = description;
    if (categoryId !== undefined) product.categoryId = categoryId;
    if (price !== undefined) product.price = price;
    if (category !== undefined) product.category = category;
    if (inStock !== undefined) product.inStock = inStock;
    if (isFeatured !== undefined) product.isFeatured = isFeatured;
    if (active !== undefined) product.active = active;

    await product.save();

    const updatedProduct = await Product.findById(product._id).populate(
      "categoryId",
      "name"
    );

    res.json({
      success: true,
      message: "Product updated successfully",
      product: updatedProduct,
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: "Server error updating product",
      error: error.message,
    });
  }
};

// @desc    Delete product
// @route   DELETE /api/products/:id
// @access  Private/Admin
const deleteProduct = async (req, res) => {
  try {
    const product = await Product.findById(req.params.id);
    if (!product) {
      return res.status(404).json({
        success: false,
        message: "Product not found",
      });
    }

    await Product.findByIdAndDelete(req.params.id);

    res.json({
      success: true,
      message: "Product deleted successfully",
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: "Server error deleting product",
      error: error.message,
    });
  }
};

// @desc    Add product review
// @route   POST /api/products/:id/reviews
// @access  Private
const addProductReview = async (req, res) => {
  try {
    const { rating, comment } = req.body;

    if (!rating || rating < 1 || rating > 5) {
      return res.status(400).json({
        success: false,
        message: "Rating must be between 1 and 5",
      });
    }

    const product = await Product.findById(req.params.id);
    if (!product) {
      return res.status(404).json({
        success: false,
        message: "Product not found",
      });
    }

    // Check if user already reviewed this product
    const existingReview = product.reviews.find(
      (review) => review.user.toString() === req.user.userId
    );

    if (existingReview) {
      return res.status(400).json({
        success: false,
        message: "You have already reviewed this product",
      });
    }

    const review = {
      user: req.user.userId,
      rating: parseInt(rating),
      comment: comment || "",
    };

    product.reviews.push(review);
    await product.updateReviewStats();

    const updatedProduct = await Product.findById(product._id)
      .populate("reviews.user", "name")
      .populate("categoryId", "name");

    res.status(201).json({
      success: true,
      message: "Review added successfully",
      product: updatedProduct,
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: "Server error adding review",
      error: error.message,
    });
  }
};

// @desc    Update product review
// @route   PUT /api/products/:id/reviews/:reviewId
// @access  Private
const updateProductReview = async (req, res) => {
  try {
    const { rating, comment } = req.body;

    if (!rating || rating < 1 || rating > 5) {
      return res.status(400).json({
        success: false,
        message: "Rating must be between 1 and 5",
      });
    }

    const product = await Product.findById(req.params.id);
    if (!product) {
      return res.status(404).json({
        success: false,
        message: "Product not found",
      });
    }

    const review = product.reviews.id(req.params.reviewId);
    if (!review) {
      return res.status(404).json({
        success: false,
        message: "Review not found",
      });
    }

    // Check if user owns this review
    if (review.user.toString() !== req.user.userId) {
      return res.status(403).json({
        success: false,
        message: "Not authorized to update this review",
      });
    }

    review.rating = parseInt(rating);
    review.comment = comment || "";

    await product.save();
    await product.updateReviewStats();

    const updatedProduct = await Product.findById(product._id)
      .populate("reviews.user", "name")
      .populate("categoryId", "name");

    res.json({
      success: true,
      message: "Review updated successfully",
      product: updatedProduct,
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: "Server error updating review",
      error: error.message,
    });
  }
};

// @desc    Delete product review
// @route   DELETE /api/products/:id/reviews/:reviewId
// @access  Private
const deleteProductReview = async (req, res) => {
  try {
    const product = await Product.findById(req.params.id);
    if (!product) {
      return res.status(404).json({
        success: false,
        message: "Product not found",
      });
    }

    const review = product.reviews.id(req.params.reviewId);
    if (!review) {
      return res.status(404).json({
        success: false,
        message: "Review not found",
      });
    }

    // Check if user owns this review or is admin
    if (
      review.user.toString() !== req.user.userId &&
      req.user.role !== "admin"
    ) {
      return res.status(403).json({
        success: false,
        message: "Not authorized to delete this review",
      });
    }

    product.reviews.pull(req.params.reviewId);
    await product.save();
    await product.updateReviewStats();

    res.json({
      success: true,
      message: "Review deleted successfully",
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: "Server error deleting review",
      error: error.message,
    });
  }
};

// @desc    Get featured products
// @route   GET /api/products/featured
// @access  Public
const getFeaturedProducts = async (req, res) => {
  try {
    const { limit = 8 } = req.query;

    const products = await Product.find({
      isFeatured: true,
      active: true,
    })
      .populate("categoryId", "name")
      .limit(limit * 1)
      .sort({ createdAt: -1 });

    res.json({
      success: true,
      data: { products },
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: "Server error fetching featured products",
      error: error.message,
    });
  }
};

// @desc    Get product statistics
// @route   GET /api/products/stats
// @access  Private/Admin
const getProductStats = async (req, res) => {
  try {
    const totalProducts = await Product.countDocuments();
    const activeProducts = await Product.countDocuments({ active: true });
    const inactiveProducts = await Product.countDocuments({ active: false });
    const inStockProducts = await Product.countDocuments({ inStock: true });
    const outOfStockProducts = await Product.countDocuments({ inStock: false });
    const featuredProducts = await Product.countDocuments({ isFeatured: true });

    // Get top-rated products
    const topRatedProducts = await Product.find({ active: true })
      .sort({ averageRating: -1 })
      .limit(5)
      .populate("categoryId", "name");

    // Get products by category
    const productsByCategory = await Product.aggregate([
      { $match: { active: true } },
      { $group: { _id: "$category", count: { $sum: 1 } } },
      { $sort: { count: -1 } },
    ]);

    // Get average price
    const avgPriceResult = await Product.aggregate([
      { $match: { active: true } },
      { $group: { _id: null, averagePrice: { $avg: "$price" } } },
    ]);
    const averagePrice = avgPriceResult[0]?.averagePrice || 0;

    res.json({
      success: true,
      data: {
        stats: {
          totalProducts,
          activeProducts,
          inactiveProducts,
          inStockProducts,
          outOfStockProducts,
          featuredProducts,
          averagePrice,
          topRatedProducts,
          productsByCategory,
        },
      },
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: "Server error fetching product statistics",
      error: error.message,
    });
  }
};

// @desc    Toggle product featured status
// @route   PUT /api/products/:id/featured
// @access  Private/Admin
const toggleFeaturedStatus = async (req, res) => {
  try {
    const product = await Product.findById(req.params.id);
    if (!product) {
      return res.status(404).json({
        success: false,
        message: "Product not found",
      });
    }

    product.isFeatured = !product.isFeatured;
    await product.save();

    res.json({
      success: true,
      message: `Product ${
        product.isFeatured ? "featured" : "unfeatured"
      } successfully`,
      product: {
        id: product._id,
        name: product.name,
        isFeatured: product.isFeatured,
      },
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: "Server error toggling featured status",
      error: error.message,
    });
  }
};

// @desc    Toggle product stock status
// @route   PUT /api/products/:id/stock
// @access  Private/Admin
const toggleStockStatus = async (req, res) => {
  try {
    const product = await Product.findById(req.params.id);
    if (!product) {
      return res.status(404).json({
        success: false,
        message: "Product not found",
      });
    }

    product.inStock = !product.inStock;
    await product.save();

    res.json({
      success: true,
      message: `Product marked as ${
        product.inStock ? "in stock" : "out of stock"
      }`,
      product: {
        id: product._id,
        name: product.name,
        inStock: product.inStock,
      },
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: "Server error toggling stock status",
      error: error.message,
    });
  }
};

module.exports = {
  getProducts,
  getProductById,
  createProduct,
  updateProduct,
  deleteProduct,
  addProductReview,
  updateProductReview,
  deleteProductReview,
  getFeaturedProducts,
  getProductStats,
  toggleFeaturedStatus,
  toggleStockStatus,
};
