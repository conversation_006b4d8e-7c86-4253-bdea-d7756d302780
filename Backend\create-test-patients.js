const mongoose = require("mongoose");
require("dotenv").config();

// Import existing models
const Patient = require("./models/Patient");

async function createTestPatients() {
  try {
    // Connect to database
    await mongoose.connect(process.env.DB_URL, {
      dbName: process.env.DB_NAME || "ecommerce",
    });
    console.log("✅ Connected to MongoDB");

    // Check existing patients
    const existingPatients = await Patient.find({});
    console.log(`📊 Found ${existingPatients.length} existing patients`);

    if (existingPatients.length > 0) {
      console.log("✅ Patients already exist. Displaying existing patients:");
      existingPatients.forEach((patient, index) => {
        console.log(
          `   ${index + 1}. Patient ${patient.name} - Age: ${
            patient.age
          } - Gender: ${patient.gender}`
        );
      });
      return;
    }

    // Create test patients
    const testPatients = [
      {
        name: "<PERSON><PERSON>",
        age: 32,
        gender: "Female",
        contactNumber: "+91 **********",
        address: "123 MG Road, Mumbai, Maharashtra",
        healthConditions: [
          {
            condition: "Hormonal Imbalance",
            diagnosedDate: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000),
            notes: "Irregular menstrual cycles",
          },
        ],
        decription:
          "Experiencing irregular menstrual cycles and mood swings for the past 3 months.",
        nextDueAppointment: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000),
        appointmentDetails: [
          {
            appointmentDate: new Date(Date.now() - 15 * 24 * 60 * 60 * 1000),
            status: "Completed",
            notes: "Prescribed hormonal supplements",
          },
        ],
      },
      {
        name: "Arjun Verma",
        age: 45,
        gender: "Male",
        contactNumber: "+91 **********",
        address: "456 Park Street, Delhi",
        healthConditions: [
          {
            condition: "Erectile Dysfunction",
            diagnosedDate: new Date(Date.now() - 60 * 24 * 60 * 60 * 1000),
            notes: "Mild performance issues",
          },
        ],
        decription:
          "Experiencing performance issues for the past 2 months. Looking for natural treatment options.",
        nextDueAppointment: new Date(Date.now() + 3 * 24 * 60 * 60 * 1000),
        appointmentDetails: [
          {
            appointmentDate: new Date(Date.now() - 20 * 24 * 60 * 60 * 1000),
            status: "Completed",
            notes: "Recommended lifestyle changes and herbal supplements",
          },
        ],
      },
      {
        name: "Radha Krishnan",
        age: 28,
        gender: "Female",
        contactNumber: "+91 **********",
        address: "789 Anna Salai, Chennai, Tamil Nadu",
        healthConditions: [
          {
            condition: "Low Libido",

            diagnosedDate: new Date(Date.now() - 45 * 24 * 60 * 60 * 1000),
          },
        ],
        decription:
          "Decreased interest in intimacy after childbirth. Seeking natural remedies.",
        nextDueAppointment: new Date(Date.now() + 10 * 24 * 60 * 60 * 1000),
        appointmentDetails: [
          {
            appointmentDate: new Date(Date.now() - 10 * 24 * 60 * 60 * 1000),
            status: "Scheduled",

            notes: "Initial consultation completed",
          },
        ],
      },
      {
        name: "Vikram Singh",
        age: 38,
        gender: "Male",
        contactNumber: "+91 **********",
        address: "321 Civil Lines, Jaipur, Rajasthan",
        healthConditions: [
          {
            condition: "Premature Ejaculation",

            diagnosedDate: new Date(Date.now() - 90 * 24 * 60 * 60 * 1000),
          },
        ],
        decription:
          "Long-standing issue affecting relationship. Previous treatments haven't been effective.",
        nextDueAppointment: new Date(Date.now() + 5 * 24 * 60 * 60 * 1000),
        appointmentDetails: [
          {
            appointmentDate: new Date(Date.now() - 25 * 24 * 60 * 60 * 1000),
            status: "Completed",

            notes: "Follow-up required in 2 weeks",
          },
        ],
      },
      {
        name: "Meera Patel",
        age: 55,
        gender: "Female",
        contactNumber: "+91 **********",
        address: "654 SG Highway, Ahmedabad, Gujarat",
        healthConditions: [
          {
            condition: "Menopause Symptoms",

            diagnosedDate: new Date(Date.now() - 120 * 24 * 60 * 60 * 1000),
          },
        ],
        decription:
          "Experiencing hot flashes, mood swings, and sleep disturbances. Looking for natural hormone therapy.",
        nextDueAppointment: new Date(Date.now() + 14 * 24 * 60 * 60 * 1000),
        appointmentDetails: [
          {
            appointmentDate: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000),
            status: "Completed",

            notes: "Prescribed herbal supplements for menopause",
          },
        ],
      },
    ];

    // Insert patients
    const createdPatients = await Patient.insertMany(testPatients);
    console.log(
      `✅ Created ${createdPatients.length} test patients successfully!`
    );

    // Display created patients
    createdPatients.forEach((patient, index) => {
      console.log(
        `   ${index + 1}. Patient ${patient.name} - Age: ${
          patient.age
        } - Gender: ${patient.gender} - Contact: ${patient.contactNumber}`
      );
    });

    console.log(
      "\n💡 Test patients created! You can now test the Consultant page."
    );
  } catch (error) {
    console.error("❌ Error:", error.message);
  } finally {
    await mongoose.disconnect();
    console.log("🔌 Disconnected from MongoDB");
  }
}

createTestPatients();
