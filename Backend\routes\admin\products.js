const express = require("express");
const router = express.Router();
const {
  createProduct,
  updateProduct,
  deleteProduct,
  getProductStats,
  toggleFeaturedStatus,
  toggleStockStatus,
} = require("../../controllers/productController");
const { protect, authorize } = require("../../middleware/auth");
const multer = require("multer");
const path = require("path");

// Set up storage engine for multer to save images with original extension
const storage = multer.diskStorage({
  destination: function (req, file, cb) {
    cb(null, "uploads/");
  },
  filename: function (req, file, cb) {
    const ext = path.extname(file.originalname);
    const uniqueSuffix = Date.now() + "-" + Math.round(Math.random() * 1e9);
    cb(null, file.fieldname + "-" + uniqueSuffix + ext);
  },
});

// File filter to accept only image files (jpeg, jpg, png, gif)
const fileFilter = function (req, file, cb) {
  const allowedTypes = /jpeg|jpg|png|gif/;
  const extname = allowedTypes.test(
    path.extname(file.originalname).toLowerCase()
  );
  const mimetype = allowedTypes.test(file.mimetype);

  if (extname && mimetype) {
    return cb(null, true);
  } else {
    cb(new Error("Only image files are allowed!"));
  }
};

const upload = multer({
  storage: storage,
  fileFilter: fileFilter,
  limits: { fileSize: 5 * 1024 * 1024 }, // 5MB limit per image
});

// All routes are protected
router.use(protect);

// Routes accessible by both admin and employee (view-only for employees)
// @route   GET /api/admin/products/stats
// @desc    Get product statistics
// @access  Private/Admin/Employee
router.get("/stats", authorize("admin", "employee"), getProductStats);

// @route   GET /api/admin/products
// @desc    Get all products (admin/employee)
// @access  Private/Admin/Employee
router.get(
  "/",
  authorize("admin", "employee"),
  require("../../controllers/productController").getProducts
);

// @route   GET /api/admin/products/:id
// @desc    Get product by ID (admin/employee)
// @access  Private/Admin/Employee
router.get(
  "/:id",
  authorize("admin", "employee"),
  require("../../controllers/productController").getProductById
);

// Admin-only routes (create, update, delete)
// @route   POST /api/admin/products
// @desc    Create new product
// @access  Private/Admin
router.post("/", authorize("admin"), upload.array("images", 5), createProduct);

// @route   PUT /api/admin/products/:id
// @desc    Update product
// @access  Private/Admin
router.put(
  "/:id",
  authorize("admin"),
  upload.array("images", 5),
  updateProduct
);

// @route   DELETE /api/admin/products/:id
// @desc    Delete product
// @access  Private/Admin
router.delete("/:id", authorize("admin"), deleteProduct);

// @route   PUT /api/admin/products/:id/featured
// @desc    Toggle product featured status
// @access  Private/Admin
router.put("/:id/featured", toggleFeaturedStatus);

// @route   PUT /api/admin/products/:id/stock
// @desc    Toggle product stock status
// @access  Private/Admin
router.put("/:id/stock", toggleStockStatus);

module.exports = router;
