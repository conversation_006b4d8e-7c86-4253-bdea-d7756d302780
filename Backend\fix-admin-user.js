require("dotenv").config();
const mongoose = require("mongoose");
const bcrypt = require("bcryptjs");
const User = require("./models/User");

async function fixAdminUser() {
  try {
    // Connect to MongoDB
    await mongoose.connect(process.env.DB_URL + process.env.DB_NAME);
    console.log("✅ Connected to MongoDB");

    // Delete existing admin user
    await User.deleteOne({ email: "<EMAIL>" });
    console.log("🗑️ Deleted existing admin user");

    // Hash password
    const salt = await bcrypt.genSalt(10);
    const hashedPassword = await bcrypt.hash("Admin123!", salt);

    // Create new admin user with proper password
    const adminUser = new User({
      name: "Admin User",
      email: "<EMAIL>",
      password: hashedPassword,
      phone: "9876543210",
      role: "admin",
      department: "Administration",
      status: "active",
      isEmailVerified: true,
      isPasswordSet: true, // Important: mark password as set
      paymentMethod: "card",
      joinedDate: Date.now(),
    });

    await adminUser.save();
    console.log("✅ New admin user created successfully!");
    console.log("📧 Email: <EMAIL>");
    console.log("🔑 Password: Admin123!");
    console.log("👤 Role: admin");
    console.log("🔐 Password Set: true");

  } catch (error) {
    console.error("❌ Error:", error);
  } finally {
    await mongoose.disconnect();
    console.log("🔌 Disconnected from MongoDB");
  }
}

fixAdminUser();
