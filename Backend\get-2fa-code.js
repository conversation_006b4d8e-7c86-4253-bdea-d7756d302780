const mongoose = require('mongoose');
const User = require('./models/User');
require('dotenv').config();

async function getTwoFactorCode() {
    try {
        // Connect to database
        await mongoose.connect(process.env.DB_URL, {
            dbName: process.env.DB_NAME || 'ecommerce',
        });
        console.log('✅ Connected to MongoDB');

        // Find the admin user
        const adminUser = await User.findOne({ email: '<EMAIL>' });
        if (adminUser && adminUser.twoFactorCode) {
            console.log('🔐 Two-Factor <NAME_EMAIL>:', adminUser.twoFactorCode);
            console.log('⏰ Code expires at:', new Date(adminUser.twoFactorExpires));
            console.log('⏰ Current time:', new Date());
            
            if (Date.now() > adminUser.twoFactorExpires) {
                console.log('❌ Code has expired!');
            } else {
                console.log('✅ Code is still valid');
            }
        } else {
            console.log('❌ No two-factor code found for admin user');
        }

    } catch (error) {
        console.error('❌ Error:', error.message);
    } finally {
        await mongoose.connection.close();
        console.log('🔌 Database connection closed');
    }
}

getTwoFactorCode();
