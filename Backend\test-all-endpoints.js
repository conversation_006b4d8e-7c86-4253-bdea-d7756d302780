const http = require("http");

const makeRequest = (path) => {
  return new Promise((resolve, reject) => {
    const options = {
      hostname: 'localhost',
      port: 5000,
      path: path,
      method: 'GET'
    };

    const req = http.request(options, (res) => {
      let data = '';
      res.on('data', (chunk) => {
        data += chunk;
      });
      res.on('end', () => {
        try {
          resolve({
            status: res.statusCode,
            data: JSON.parse(data)
          });
        } catch (e) {
          resolve({
            status: res.statusCode,
            data: data
          });
        }
      });
    });

    req.on('error', (err) => {
      reject(err);
    });

    req.end();
  });
};

const testAllEndpoints = async () => {
  try {
    console.log("🧪 Testing All Product API Endpoints...\n");

    // Test 1: Get all products
    console.log("1️⃣ Testing GET /api/admin/products");
    const productsResponse = await makeRequest("/api/admin/products");
    console.log(`✅ Status: ${productsResponse.status}`);
    console.log(`📊 Products found: ${productsResponse.data.data.products.length}`);
    console.log(`📄 Sample product: ${productsResponse.data.data.products[0]?.name || 'None'}\n`);

    // Test 2: Get product stats
    console.log("2️⃣ Testing GET /api/admin/products/stats");
    const statsResponse = await makeRequest("/api/admin/products/stats");
    console.log(`✅ Status: ${statsResponse.status}`);
    console.log(`📊 Total Products: ${statsResponse.data.data.stats.totalProducts}`);
    console.log(`💰 Average Price: ₹${Math.round(statsResponse.data.data.stats.averagePrice)}\n`);

    // Test 3: Get featured products
    console.log("3️⃣ Testing GET /api/user/products/featured");
    const featuredResponse = await makeRequest("/api/user/products/featured");
    console.log(`✅ Status: ${featuredResponse.status}`);
    console.log(`⭐ Featured products: ${featuredResponse.data.data.products.length}\n`);

    console.log("🎉 All API endpoints are working correctly with proper response structure!");
    
  } catch (error) {
    console.error("❌ API Test Error:", error.message);
  }
};

testAllEndpoints();
