# dr-kumar-client-agnistoka
backend for dr kumar client

## Authentication & Authorization

- All protected routes require a JWT token in the `Authorization` header as `Bearer <token>`.
- Use `/api/user/users/register` and `/api/user/users/login` to obtain a token.
- The `protect` middleware checks for a valid token and attaches the user to `req.user`.
- The `authorize(...roles)` middleware restricts access to users with specific roles (e.g., 'admin', 'user', 'employee').

**Example request:**

```
GET /api/user/orders
Authorization: Bearer <your_token_here>
```
