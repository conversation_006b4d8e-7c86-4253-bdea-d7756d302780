const mongoose = require('mongoose');

const CartSchema = new mongoose.Schema({
    user: {
        type: mongoose.Schema.Types.ObjectId,
        ref: 'User',
        required: true
    },
    items: [
        {
            product: {
                type: mongoose.Schema.Types.ObjectId,
                ref: 'Product',
                required: true
            },
            quantity: {
                type: Number,
                required: true,
                min: 1,
                default: 1
            }
        }
    ],
    createdAt: {
        type: Date,
        default: Date.now
    },
    updateAt:{
        type: Date,
        default: Date.now
    }
});

CartSchema.pre('save', function (next) {
    if (!this.isNew) {
        this.updateAt = Date.now();
    }                                           
    next();
}); 

CartSchema.pre('findOneAndUpdate', function (next) {
    this.set({ updateAt: Date.now() });                                 
    next();     
});     

CartSchema.pre('updateOne', function (next) {
    this.set({ updateAt: Date.now() });
    next();
});

module.exports = mongoose.model('Cart', CartSchema);