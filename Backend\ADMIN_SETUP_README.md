# 🔐 Admin Setup Guide - Dr <PERSON> API

This guide will help you create an authorized admin user using MongoDB Compass and test it with Postman.

## 📋 Prerequisites

Before starting, make sure you have:
- MongoDB Compass installed
- Postman installed
- Backend server running (`npm start` or `node index.js`)
- MongoDB connection string from your `.env` file

## 🗄️ Step 1: Connect to MongoDB using MongoDB Compass

### 1.1 Open MongoDB Compass
- Launch MongoDB Compass application

### 1.2 Connect to Database
- Use your connection string from `.env` file (DB_URL)
- Default database name: `ecommerce` (or check DB_NAME in your `.env`)
- Click "Connect"

### 1.3 Navigate to Users Collection
- Find your database (usually named `ecommerce`)
- Look for the `users` collection
- If it doesn't exist, it will be created when you add the first user

## 👤 Step 2: Create Admin User in MongoDB Compass

### 2.1 Method 1: Direct Database Insert

Click on the `users` collection and then "INSERT DOCUMENT". Add this JSON:

```json
{
  "name": "Admin User",
  "email": "<EMAIL>",
  "password": "$2a$10$rOzJqQZ8Kx8Kx8Kx8Kx8KuXxXxXxXxXxXxXxXxXxXxXxXxXxXxXxX",
  "role": "admin",
  "phone": "**********",
  "status": "active",
  "department": "Administration",
  "paymentMethod": "admin_account",
  "isEmailVerified": true,
  "shippingAddress": [
    {
      "fullName": "Admin User",
      "address": "Admin Office",
      "city": "Admin City",
      "postalCode": "000000",
      "country": "India"
    }
  ],
  "profileImage": {
    "url": "",
    "publicId": ""
  },
  "joinedDate": "2024-01-01T00:00:00.000Z",
  "createdAt": "2024-01-01T00:00:00.000Z",
  "updatedAt": "2024-01-01T00:00:00.000Z"
}
```

**Note:** The password above is hashed version of `admin123`. You can generate your own using bcrypt.

### 2.2 Method 2: Register via API then Update Role

1. **Register a normal user first** using Postman
2. **Find the user** in MongoDB Compass
3. **Update the role** from `user` to `admin`

## 🔧 Step 3: Generate Hashed Password (Optional)

If you want to create your own password, use this Node.js script:

```javascript
const bcrypt = require('bcryptjs');

async function hashPassword() {
    const password = 'your_admin_password';
    const salt = await bcrypt.genSalt(10);
    const hashedPassword = await bcrypt.hash(password, salt);
    console.log('Hashed Password:', hashedPassword);
}

hashPassword();
```

## 📮 Step 4: Test Admin Login with Postman

### 4.1 Import Collections
- Import the Postman collections from the backend folder
- `Dr-Kumar-Admin-API.postman_collection.json`
- `Dr-Kumar-User-API.postman_collection.json`

### 4.2 Set Environment Variables
Create a new environment in Postman with:
```
base_url: http://localhost:5000/api
admin_token: (will be set after login)
user_token: (will be set after login)
```

### 4.3 Login as Admin

**Endpoint:** `POST {{base_url}}/users/login`

**Request Body:**
```json
{
  "email": "<EMAIL>",
  "password": "admin123"
}
```

**Expected Response:**
```json
{
  "success": true,
  "message": "Login successful",
  "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
  "user": {
    "id": "user_id_here",
    "name": "Admin User",
    "email": "<EMAIL>",
    "role": "admin",
    "status": "active",
    "isEmailVerified": true
  }
}
```

### 4.4 Set Admin Token
- Copy the `token` from the response
- Set it as `admin_token` in your Postman environment

## ✅ Step 5: Test Admin Endpoints

Now you can test admin-only endpoints:

### 5.1 Test Banner Management
**Endpoint:** `GET {{base_url}}/admin/banners`
**Headers:** `Authorization: Bearer {{admin_token}}`

### 5.2 Test User Management
**Endpoint:** `GET {{base_url}}/admin/users`
**Headers:** `Authorization: Bearer {{admin_token}}`

### 5.3 Test Product Management
**Endpoint:** `GET {{base_url}}/admin/products`
**Headers:** `Authorization: Bearer {{admin_token}}`

## 🔍 Step 6: Verify Admin Access in MongoDB Compass

### 6.1 Check User Document
- Go to `users` collection
- Find your admin user
- Verify these fields:
  - `role: "admin"`
  - `status: "active"`
  - `isEmailVerified: true`

### 6.2 Update Admin Details (Optional)
You can update admin details directly in MongoDB Compass:
- Double-click on any field to edit
- Save changes

## 🚨 Troubleshooting

### Issue 1: Login Failed
**Problem:** Invalid credentials error
**Solution:** 
- Check if email exists in database
- Verify password hash is correct
- Ensure user status is "active"

### Issue 2: Access Denied
**Problem:** "User role not authorized" error
**Solution:**
- Verify role is set to "admin" in database
- Check if JWT token is valid
- Ensure token is properly set in Postman headers

### Issue 3: Database Connection
**Problem:** Cannot connect to MongoDB
**Solution:**
- Check MongoDB is running
- Verify connection string in `.env`
- Check network connectivity

## 📝 Important Notes

1. **Security:** Change default admin password in production
2. **Email Verification:** Set `isEmailVerified: true` for admin users
3. **Role Hierarchy:** Available roles are `user`, `employee`, `admin`
4. **Token Expiry:** JWT tokens expire in 7 days by default
5. **Environment:** Use different admin credentials for production

## 🎯 Quick Admin Creation Script

For development, you can use this quick script:

```javascript
// Run this in your backend directory
const User = require('./models/User');
const bcrypt = require('bcryptjs');
const mongoose = require('mongoose');

async function createAdmin() {
    const hashedPassword = await bcrypt.hash('admin123', 10);
    
    const admin = new User({
        name: 'Admin User',
        email: '<EMAIL>',
        password: hashedPassword,
        role: 'admin',
        phone: '**********',
        status: 'active',
        paymentMethod: 'admin_account',
        isEmailVerified: true,
        shippingAddress: [{
            fullName: 'Admin User',
            address: 'Admin Office',
            city: 'Admin City',
            postalCode: '000000',
            country: 'India'
        }]
    });
    
    await admin.save();
    console.log('Admin created successfully!');
}
```

## 📞 Support

If you encounter any issues:
1. Check server logs for errors
2. Verify MongoDB connection
3. Ensure all required fields are present
4. Check JWT_SECRET in environment variables

---

**Happy Testing! 🚀**
