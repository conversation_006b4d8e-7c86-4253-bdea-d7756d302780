const mongoose = require('mongoose');
const Product = require('../models/Products');
const Category = require('../models/Categories');
require('dotenv').config();

const connectDB = async () => {
  try {
    await mongoose.connect(process.env.DB_URL, {
      dbName: process.env.DB_NAME || 'ecommerce'
    });
    console.log('MongoDB connected successfully');
  } catch (error) {
    console.error('MongoDB connection error:', error);
    process.exit(1);
  }
};

const seedProducts = async () => {
  try {
    await connectDB();
    
    // Get existing categories
    const categories = await Category.find();
    console.log('Found categories:', categories.map(c => ({ id: c._id, name: c.name })));
    
    if (categories.length === 0) {
      console.log('No categories found. Please add categories first.');
      process.exit(1);
    }
    
    // Use the first category for all products
    const categoryId = categories[0]._id;
    const categoryName = categories[0].name;
    
    // Sample products data
    const sampleProducts = [
      {
        name: "Power Stride Energy Drink",
        description: "Premium energy drink specially formulated for reproductive health and vitality. Contains natural ingredients to boost energy and stamina.",
        categoryId: categoryId,
        category: categoryName,
        price: 249,
        inStock: true,
        isFeatured: true,
        images: [
          {
            url: "/uploads/power-stride.jpg",
            altText: "Power Stride Energy Drink"
          }
        ],
        active: true
      },
      {
        name: "Ashwagandha Premium Capsules",
        description: "High-quality Ashwagandha capsules for stress relief and improved energy levels. 100% natural and organic.",
        categoryId: categoryId,
        category: categoryName,
        price: 599,
        inStock: true,
        isFeatured: true,
        images: [
          {
            url: "/uploads/ashwagandha.jpg",
            altText: "Ashwagandha Premium Capsules"
          }
        ],
        active: true
      },
      {
        name: "Triphala Churna Powder",
        description: "Traditional Ayurvedic digestive support powder. Helps with digestion and overall wellness.",
        categoryId: categoryId,
        category: categoryName,
        price: 149,
        inStock: true,
        isFeatured: false,
        images: [
          {
            url: "/uploads/triphala.jpg",
            altText: "Triphala Churna Powder"
          }
        ],
        active: true
      },
      {
        name: "Vitamin D3 Tablets",
        description: "Essential Vitamin D3 supplements for bone health and immune system support.",
        categoryId: categoryId,
        category: categoryName,
        price: 399,
        inStock: true,
        isFeatured: false,
        images: [
          {
            url: "/uploads/vitamin-d3.jpg",
            altText: "Vitamin D3 Tablets"
          }
        ],
        active: true
      },
      {
        name: "Omega-3 Fish Oil Capsules",
        description: "Premium quality Omega-3 fish oil capsules for heart and brain health.",
        categoryId: categoryId,
        category: categoryName,
        price: 799,
        inStock: false,
        isFeatured: true,
        images: [
          {
            url: "/uploads/omega-3.jpg",
            altText: "Omega-3 Fish Oil Capsules"
          }
        ],
        active: true
      }
    ];
    
    // Clear existing products
    await Product.deleteMany({});
    console.log('Cleared existing products');
    
    // Insert sample products
    const insertedProducts = await Product.insertMany(sampleProducts);
    console.log(`Successfully inserted ${insertedProducts.length} products:`);
    
    insertedProducts.forEach(product => {
      console.log(`- ${product.name} (₹${product.price}) - ${product.inStock ? 'In Stock' : 'Out of Stock'} - ${product.isFeatured ? 'Featured' : 'Regular'}`);
    });
    
    console.log('\n✅ Database seeding completed successfully!');
    process.exit(0);
    
  } catch (error) {
    console.error('Error seeding database:', error);
    process.exit(1);
  }
};

// Run the seeding script
seedProducts();
