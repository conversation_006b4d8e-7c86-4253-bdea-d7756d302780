Stack trace:
Frame         Function      Args
0007FFFFB6A0  00021006118E (00021028DEE8, 000210272B3E, 000000000000, 0007FFFFA5A0) msys-2.0.dll+0x2118E
0007FFFFB6A0  0002100469BA (000000000000, 000000000000, 000000000000, 000000000004) msys-2.0.dll+0x69BA
0007FFFFB6A0  0002100469F2 (00021028DF99, 0007FFFFB558, 000000000000, 000000000000) msys-2.0.dll+0x69F2
0007FFFFB6A0  00021006A41E (000000000000, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2A41E
0007FFFFB6A0  00021006A545 (0007FFFFB6B0, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2A545
0001004F94B7  00021006B9A5 (0007FFFFB6B0, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2B9A5
End of stack trace
Loaded modules:
000100400000 bash.exe
7FFFED190000 ntdll.dll
7FFFEC250000 KERNEL32.DLL
7FFFEA5C0000 KERNELBASE.dll
7FFFECF90000 USER32.dll
7FFFEA590000 win32u.dll
7FFFECB10000 GDI32.dll
000210040000 msys-2.0.dll
7FFFEA300000 gdi32full.dll
7FFFEA430000 msvcp_win.dll
7FFFEAAE0000 ucrtbase.dll
7FFFEAEE0000 advapi32.dll
7FFFEAE30000 msvcrt.dll
7FFFEC1A0000 sechost.dll
7FFFEAC00000 bcrypt.dll
7FFFEC6C0000 RPCRT4.dll
7FFFE9A70000 CRYPTBASE.DLL
7FFFEA280000 bcryptPrimitives.dll
7FFFEB350000 IMM32.DLL
