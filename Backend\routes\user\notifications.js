const express = require('express');
const router = express.Router();
const {
  getNotifications,
  getNotificationById,
  markNotificationRead,
  markAllNotificationsRead,
  getUnreadNotificationCount,
  deleteNotification
} = require('../../controllers/notificationController');
const { protect } = require('../../middleware/authMiddleware');

// Apply authentication middleware to all routes
router.use(protect);

// @route   GET /api/user/notifications
// @desc    Get all notifications for the authenticated user
// @access  Private
router.get('/', getNotifications);

// @route   GET /api/user/notifications/unread-count
// @desc    Get unread notification count
// @access  Private
router.get('/unread-count', getUnreadNotificationCount);

// @route   PUT /api/user/notifications/mark-all-read
// @desc    Mark all notifications as read
// @access  Private
router.put('/mark-all-read', markAllNotificationsRead);

// @route   GET /api/user/notifications/:id
// @desc    Get notification by ID
// @access  Private
router.get('/:id', getNotificationById);

// @route   PUT /api/user/notifications/:id/read
// @desc    Mark notification as read
// @access  Private
router.put('/:id/read', markNotificationRead);

// @route   DELETE /api/user/notifications/:id
// @desc    Delete notification
// @access  Private
router.delete('/:id', deleteNotification);

module.exports = router;
