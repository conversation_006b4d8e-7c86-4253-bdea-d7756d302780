const express = require("express");
const router = express.Router();

// Import controller functions
const {
  getUserProfile,
  updateUserProfile,
} = require("../../controllers/userController");

// @route   GET /api/user/users/profile
// @desc    Get user profile
// @access  Private
router.get("/profile", getUserProfile);

// @route   PUT /api/user/users/profile
// @desc    Update user profile
// @access  Private
router.put("/profile", updateUserProfile);

module.exports = router;
