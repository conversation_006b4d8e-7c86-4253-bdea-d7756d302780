const mongoose = require("mongoose");
const bcrypt = require("bcryptjs");
const User = require("./models/User");
require("dotenv").config();

async function createTestUsers() {
  try {
    // Connect to database
    await mongoose.connect(process.env.DB_URL, {
      dbName: process.env.DB_NAME || "ecommerce",
    });
    console.log("✅ Connected to MongoDB");

    // Check if admin user already exists
    const existingAdmin = await User.findOne({ email: "<EMAIL>" });
    if (existingAdmin) {
      console.log("⚠️  Admin user already exists");
    } else {
      // Create admin user
      const adminPassword = await bcrypt.hash("Admin123!", 10);
      const adminUser = new User({
        name: "Admin User",
        email: "<EMAIL>",
        password: adminPassword,
        phone: "9876543210",
        role: "admin",
        department: "Administration",
        paymentMethod: "card",
        status: "active",
        isEmailVerified: true,
        joinedDate: new Date(),
      });
      await adminUser.save();
      console.log("✅ Admin user created: <EMAIL> / Admin123!");
    }

    // Check if employee user already exists
    const existingEmployee = await User.findOne({
      email: "<EMAIL>",
    });
    if (existingEmployee) {
      console.log("⚠️  Employee user already exists");
    } else {
      // Create employee user
      const employeePassword = await bcrypt.hash("Employee123!", 10);
      const employeeUser = new User({
        name: "Employee User",
        email: "<EMAIL>",
        password: employeePassword,
        phone: "8765432109",
        role: "employee",
        department: "Sales",
        paymentMethod: "card",
        status: "active",
        isEmailVerified: true,
        joinedDate: new Date(),
      });
      await employeeUser.save();
      console.log(
        "✅ Employee user created: <EMAIL> / Employee123!"
      );
    }

    console.log("\n🎉 Test users are ready!");
    console.log("📝 You can now test the authentication flow:");
    console.log("   Admin: <EMAIL> / Admin123!");
    console.log("   Employee: <EMAIL> / Employee123!");
  } catch (error) {
    console.error("❌ Error creating test users:", error.message);
  } finally {
    await mongoose.connection.close();
    console.log("🔌 Database connection closed");
  }
}

createTestUsers();
