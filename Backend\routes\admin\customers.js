const express = require('express');
const router = express.Router();
const {
    getAllCustomers,
    getCustomerById,
    updateCustomer,
    deleteCustomer,
    getCustomerStats,
    getCustomerOrders
} = require('../../controllers/customerController');
const { protect, authorize } = require('../../middleware/auth');

// All routes are protected and require admin/employee access
router.use(protect);
router.use(authorize('admin', 'employee'));

// @route   GET /api/admin/customers
// @desc    Get all customers (admin)
// @access  Private/Admin
router.get('/', getAllCustomers);

// @route   GET /api/admin/customers/stats
// @desc    Get customer statistics
// @access  Private/Admin
router.get('/stats', getCustomerStats);

// @route   GET /api/admin/customers/:id
// @desc    Get customer by ID (admin)
// @access  Private/Admin
router.get('/:id', getCustomerById);

// @route   PUT /api/admin/customers/:id
// @desc    Update customer (admin)
// @access  Private/Admin
router.put('/:id', updateCustomer);

// @route   DELETE /api/admin/customers/:id
// @desc    Delete customer (admin)
// @access  Private/Admin
router.delete('/:id', deleteCustomer);

// @route   GET /api/admin/customers/:id/orders
// @desc    Get customer orders (admin)
// @access  Private/Admin
router.get('/:id/orders', getCustomerOrders);

module.exports = router;
