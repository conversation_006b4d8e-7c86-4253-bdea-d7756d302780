const mongoose = require('mongoose');
require('dotenv').config();

// Import models
const Order = require('./models/Order');
const User = require('./models/User');
const Product = require('./models/Products');

async function createTestOrders() {
    try {
        // Connect to database
        await mongoose.connect(process.env.DB_URL, {
            dbName: process.env.DB_NAME || 'ecommerce',
        });
        console.log('✅ Connected to MongoDB');

        // Check if we have users and products
        const users = await User.find({});
        const products = await Product.find({});

        console.log(`📊 Found ${users.length} users and ${products.length} products`);

        if (users.length === 0) {
            console.log('❌ No users found. Please create users first.');
            return;
        }

        if (products.length === 0) {
            console.log('❌ No products found. Please create products first.');
            return;
        }

        // Check if orders already exist
        const existingOrders = await Order.find({});
        if (existingOrders.length > 0) {
            console.log(`✅ Found ${existingOrders.length} existing orders in database`);
            console.log('Orders already exist. Skipping creation.');
            return;
        }

        // Create test orders
        const testOrders = [];
        const statuses = ['pending', 'processing', 'shipped', 'delivered', 'cancelled'];

        for (let i = 0; i < 10; i++) {
            const randomUser = users[Math.floor(Math.random() * users.length)];
            const randomProduct = products[Math.floor(Math.random() * products.length)];
            const randomStatus = statuses[Math.floor(Math.random() * statuses.length)];
            const quantity = Math.floor(Math.random() * 3) + 1;
            const price = randomProduct.price || 100;
            const totalAmount = quantity * price;

            const order = {
                user: randomUser._id,
                items: [{
                    product: randomProduct._id,
                    quantity: quantity,
                    price: price
                }],
                totalAmount: totalAmount,
                status: randomStatus,
                shippingAddress: {
                    addressLine1: `${Math.floor(Math.random() * 999) + 1} Test Street`,
                    city: ['Mumbai', 'Delhi', 'Bangalore', 'Chennai', 'Kolkata'][Math.floor(Math.random() * 5)],
                    state: ['Maharashtra', 'Delhi', 'Karnataka', 'Tamil Nadu', 'West Bengal'][Math.floor(Math.random() * 5)],
                    postalCode: `${Math.floor(Math.random() * 900000) + 100000}`,
                    country: 'India'
                },
                createdAt: new Date(Date.now() - Math.random() * 30 * 24 * 60 * 60 * 1000), // Random date within last 30 days
            };

            testOrders.push(order);
        }

        // Insert test orders
        const createdOrders = await Order.insertMany(testOrders);
        console.log(`✅ Created ${createdOrders.length} test orders successfully!`);

        // Display summary
        console.log('\n📋 Order Summary:');
        const ordersByStatus = await Order.aggregate([
            { $group: { _id: '$status', count: { $sum: 1 } } }
        ]);

        ordersByStatus.forEach(status => {
            console.log(`   ${status._id}: ${status.count} orders`);
        });

        console.log('\n💡 You can now test the Orders page with real data!');

    } catch (error) {
        console.error('❌ Error creating test orders:', error);
    } finally {
        await mongoose.disconnect();
        console.log('🔌 Disconnected from MongoDB');
    }
}

// Run the function
createTestOrders();
