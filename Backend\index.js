const express = require("express");
const dotenv = require("dotenv");
dotenv.config();
const morgan = require("morgan");
const connectDB = require("./config/db");
const cors = require("cors");
const app = express();

const port = process.env.PORT || 5000;

// Middleware
app.use(express.json({ limit: "10mb" }));
app.use(express.urlencoded({ extended: true, limit: "10mb" }));

// CORS configuration
app.use(cors());

// Temporarily disable logging middleware for testing
// app.use(morgan("combined"));

// Request logging middleware with error handling
app.use((req, res, next) => {
  try {
    console.log(`[${new Date().toISOString()}] ${req.method} ${req.path}`);
    if (req.body && Object.keys(req.body).length > 0) {
      console.log("Request Body:", JSON.stringify(req.body, null, 2));
    }
    next();
  } catch (error) {
    console.error("Logging middleware error:", error);
    next();
  }
});

// Multer setup for file uploads
// Run: npm install multer
const multer = require("multer");
const upload = multer({ dest: "uploads/" }); // Default: saves files to uploads/ directory

// Authentication routes
app.use("/api/auth", require("./routes/auth"));

// Admin routes - enabling gradually
// app.use("/api/admin/banners", require("./routes/admin/banners"));
app.use("/api/admin/categories", require("./routes/admin/categories-simple"));
app.use("/api/admin/complaints", require("./routes/admin/complaints"));
app.use("/api/admin/orders", require("./routes/admin/orders-simple"));
app.use("/api/admin/patients", require("./routes/admin/patients"));
app.use("/api/admin/products", require("./routes/admin/products"));
app.use("/api/admin/returns", require("./routes/admin/returns"));
app.use("/api/admin/reviews", require("./routes/admin/reviews"));
app.use("/api/admin/staff", require("./routes/admin/staff"));
// app.use("/api/admin/users", require("./routes/admin/users"));

// User routes - enabling simplified versions
// app.use("/api/user/cart", require("./routes/user/cart"));
// app.use("/api/user/orders", require("./routes/user/orders"));
app.use("/api/user/products", require("./routes/user/products"));
app.use("/api/user", require("./routes/user/users"));
app.use("/api/user/users", require("./routes/user/users-simple"));

// Static file serving for uploads
app.use("/uploads", express.static("uploads"));

// Health check endpoint
app.get("/api/health", (req, res) => {
  res.json({
    success: true,
    message: "Dr. Kumar Admin Backend is running",
    timestamp: new Date().toISOString(),
    environment: process.env.NODE_ENV || "development",
  });
});

// 404 handler for undefined routes
app.use((req, res) => {
  res.status(404).json({
    success: false,
    message: `Route ${req.originalUrl} not found`,
  });
});

// Global error handler with comprehensive error handling
app.use((err, req, res, next) => {
  console.error(`[ERROR] ${new Date().toISOString()}:`, err.stack);

  // Handle different types of errors
  let error = { ...err };
  error.message = err.message;

  // Mongoose bad ObjectId
  if (err.name === "CastError") {
    const message = "Resource not found";
    error = { message, statusCode: 404 };
  }

  // Mongoose duplicate key
  if (err.code === 11000) {
    const message = "Duplicate field value entered";
    error = { message, statusCode: 400 };
  }

  // Mongoose validation error
  if (err.name === "ValidationError") {
    const message = Object.values(err.errors)
      .map((val) => val.message)
      .join(", ");
    error = { message, statusCode: 400 };
  }

  // JWT errors
  if (err.name === "JsonWebTokenError") {
    const message = "Invalid token";
    error = { message, statusCode: 401 };
  }

  if (err.name === "TokenExpiredError") {
    const message = "Token expired";
    error = { message, statusCode: 401 };
  }

  res.status(error.statusCode || 500).json({
    success: false,
    message: error.message || "Internal Server Error",
    error: process.env.NODE_ENV === "development" ? err.stack : undefined,
  });
});

const startServer = async () => {
  try {
    // Connect to database
    await connectDB();
    console.log("✅ Database connected successfully");

    // Start server
    const server = app.listen(port, () => {
      console.log("🚀 Dr. Kumar Admin Backend Server Started");
      console.log(`📍 Server running on: http://localhost:${port}`);
      console.log(`🌍 Environment: ${process.env.NODE_ENV || "development"}`);
      console.log(`📊 Health check: http://localhost:${port}/api/health`);
      console.log("📝 Server logs will appear below...\n");
    });

    // Handle server shutdown gracefully
    process.on("SIGTERM", () => {
      console.log("SIGTERM received. Shutting down gracefully...");
      server.close(() => {
        console.log("Process terminated");
      });
    });
  } catch (error) {
    console.error("❌ Failed to start server:", error);
    process.exit(1);
  }
};

// Handle unhandled promise rejections
process.on("unhandledRejection", (err, promise) => {
  console.error(`Unhandled Promise Rejection: ${err.message}`);
  console.error(err.stack);
  process.exit(1);
});

// Handle uncaught exceptions
process.on("uncaughtException", (err) => {
  console.error(`Uncaught Exception: ${err.message}`);
  console.error(err.stack);
  process.exit(1);
});

startServer();
