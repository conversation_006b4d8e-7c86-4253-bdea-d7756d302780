const Coupon = require('../models/Coupons');

// @desc    Get all coupons
// @route   GET /api/admin/coupons
// @access  Private/Admin
const getAllCoupons = async (req, res) => {
    try {
        const { page = 1, limit = 10, search, status } = req.query;

        // Build query object
        const query = {};
        
        if (search) {
            query.$or = [
                { code: { $regex: search, $options: 'i' } },
                { description: { $regex: search, $options: 'i' } }
            ];
        }

        if (status && status !== 'all') {
            if (status === 'active') {
                query.isActive = true;
                query.validTo = { $gte: new Date() };
            } else if (status === 'inactive') {
                query.isActive = false;
            } else if (status === 'expired') {
                query.validTo = { $lt: new Date() };
            }
        }

        const coupons = await Coupon.find(query)
            .sort({ createdAt: -1 })
            .limit(limit * 1)
            .skip((page - 1) * limit);

        const total = await Coupon.countDocuments(query);

        res.json({
            success: true,
            coupons,
            pagination: {
                page: parseInt(page),
                limit: parseInt(limit),
                total,
                pages: Math.ceil(total / limit)
            }
        });
    } catch (error) {
        console.error('Get coupons error:', error);
        res.status(500).json({
            success: false,
            message: 'Failed to fetch coupons'
        });
    }
};

// @desc    Get coupon by ID
// @route   GET /api/admin/coupons/:id
// @access  Private/Admin
const getCouponById = async (req, res) => {
    try {
        const coupon = await Coupon.findById(req.params.id);

        if (!coupon) {
            return res.status(404).json({
                success: false,
                message: 'Coupon not found'
            });
        }

        res.json({
            success: true,
            coupon
        });
    } catch (error) {
        console.error('Get coupon by ID error:', error);
        res.status(500).json({
            success: false,
            message: 'Failed to fetch coupon'
        });
    }
};

// @desc    Create new coupon
// @route   POST /api/admin/coupons
// @access  Private/Admin
const createCoupon = async (req, res) => {
    try {
        const {
            code,
            description,
            type,
            value,
            minOrder,
            maxDiscount,
            usageLimit,
            validFrom,
            validTo,
            isActive = true
        } = req.body;

        // Check if coupon code already exists
        const existingCoupon = await Coupon.findOne({ code: code.toUpperCase() });
        if (existingCoupon) {
            return res.status(400).json({
                success: false,
                message: 'Coupon code already exists'
            });
        }

        const coupon = new Coupon({
            code: code.toUpperCase(),
            description,
            type,
            value,
            minOrder,
            maxDiscount,
            usageLimit,
            validFrom,
            validTo,
            isActive
        });

        await coupon.save();

        res.status(201).json({
            success: true,
            message: 'Coupon created successfully',
            coupon
        });
    } catch (error) {
        console.error('Create coupon error:', error);
        res.status(500).json({
            success: false,
            message: 'Failed to create coupon'
        });
    }
};

// @desc    Update coupon
// @route   PUT /api/admin/coupons/:id
// @access  Private/Admin
const updateCoupon = async (req, res) => {
    try {
        const coupon = await Coupon.findById(req.params.id);

        if (!coupon) {
            return res.status(404).json({
                success: false,
                message: 'Coupon not found'
            });
        }

        const {
            code,
            description,
            type,
            value,
            minOrder,
            maxDiscount,
            usageLimit,
            validFrom,
            validTo,
            isActive
        } = req.body;

        // Check if new code already exists (if code is being changed)
        if (code && code.toUpperCase() !== coupon.code) {
            const existingCoupon = await Coupon.findOne({ code: code.toUpperCase() });
            if (existingCoupon) {
                return res.status(400).json({
                    success: false,
                    message: 'Coupon code already exists'
                });
            }
            coupon.code = code.toUpperCase();
        }

        // Update fields
        if (description) coupon.description = description;
        if (type) coupon.type = type;
        if (value !== undefined) coupon.value = value;
        if (minOrder !== undefined) coupon.minOrder = minOrder;
        if (maxDiscount !== undefined) coupon.maxDiscount = maxDiscount;
        if (usageLimit !== undefined) coupon.usageLimit = usageLimit;
        if (validFrom) coupon.validFrom = validFrom;
        if (validTo) coupon.validTo = validTo;
        if (isActive !== undefined) coupon.isActive = isActive;

        await coupon.save();

        res.json({
            success: true,
            message: 'Coupon updated successfully',
            coupon
        });
    } catch (error) {
        console.error('Update coupon error:', error);
        res.status(500).json({
            success: false,
            message: 'Failed to update coupon'
        });
    }
};

// @desc    Delete coupon
// @route   DELETE /api/admin/coupons/:id
// @access  Private/Admin
const deleteCoupon = async (req, res) => {
    try {
        const coupon = await Coupon.findById(req.params.id);

        if (!coupon) {
            return res.status(404).json({
                success: false,
                message: 'Coupon not found'
            });
        }

        await Coupon.findByIdAndDelete(req.params.id);

        res.json({
            success: true,
            message: 'Coupon deleted successfully'
        });
    } catch (error) {
        console.error('Delete coupon error:', error);
        res.status(500).json({
            success: false,
            message: 'Failed to delete coupon'
        });
    }
};

// @desc    Toggle coupon status
// @route   PUT /api/admin/coupons/:id/status
// @access  Private/Admin
const toggleCouponStatus = async (req, res) => {
    try {
        const coupon = await Coupon.findById(req.params.id);

        if (!coupon) {
            return res.status(404).json({
                success: false,
                message: 'Coupon not found'
            });
        }

        coupon.isActive = !coupon.isActive;
        await coupon.save();

        res.json({
            success: true,
            message: `Coupon ${coupon.isActive ? 'activated' : 'deactivated'} successfully`,
            coupon
        });
    } catch (error) {
        console.error('Toggle coupon status error:', error);
        res.status(500).json({
            success: false,
            message: 'Failed to toggle coupon status'
        });
    }
};

// @desc    Validate coupon
// @route   POST /api/user/coupons/validate
// @access  Public
const validateCoupon = async (req, res) => {
    try {
        const { code } = req.body;

        const coupon = await Coupon.findOne({ 
            code: code.toUpperCase(),
            isActive: true,
            validFrom: { $lte: new Date() },
            validTo: { $gte: new Date() }
        });

        if (!coupon) {
            return res.status(404).json({
                success: false,
                message: 'Invalid or expired coupon code'
            });
        }

        // Check usage limit
        if (coupon.usageLimit && coupon.used >= coupon.usageLimit) {
            return res.status(400).json({
                success: false,
                message: 'Coupon usage limit exceeded'
            });
        }

        res.json({
            success: true,
            message: 'Coupon is valid',
            coupon: {
                code: coupon.code,
                type: coupon.type,
                value: coupon.value,
                minOrder: coupon.minOrder,
                maxDiscount: coupon.maxDiscount
            }
        });
    } catch (error) {
        console.error('Validate coupon error:', error);
        res.status(500).json({
            success: false,
            message: 'Failed to validate coupon'
        });
    }
};

module.exports = {
    getAllCoupons,
    getCouponById,
    createCoupon,
    updateCoupon,
    deleteCoupon,
    toggleCouponStatus,
    validateCoupon
};
