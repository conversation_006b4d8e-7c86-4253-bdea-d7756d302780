import { useMemo } from 'react';
import { authService } from '@/services/authService';

export interface RolePermissions {
  canAdd: boolean;
  canEdit: boolean;
  canDelete: boolean;
  canView: boolean;
  canExport: boolean;
  canRefresh: boolean;
  isAdmin: boolean;
  isEmployee: boolean;
  userRole: string;
}

export interface PagePermissions {
  products: RolePermissions;
  orders: RolePermissions;
  customers: RolePermissions;
  inventory: RolePermissions;
  consultant: RolePermissions;
  coupons: RolePermissions;
  staff: RolePermissions;
  calendar: RolePermissions;
  feedback: RolePermissions;
  reviews: RolePermissions;
  returns: RolePermissions;
  notifications: RolePermissions;
  profile: RolePermissions;
}

const useRolePermissions = (): PagePermissions => {
  const user = authService.getUser();
  const userRole = user?.role || 'employee';
  const isAdmin = userRole === 'admin';
  const isEmployee = userRole === 'employee';

  const permissions = useMemo(() => {
    // Base permissions for all authenticated users
    const basePermissions: RolePermissions = {
      canView: true,
      canExport: true,
      canRefresh: true,
      canAdd: isAdmin,
      canEdit: isAdmin,
      canDelete: isAdmin,
      isAdmin,
      isEmployee,
      userRole,
    };

    // Full access permissions (no restrictions)
    const fullAccess: RolePermissions = {
      ...basePermissions,
      canAdd: true,
      canEdit: true,
      canDelete: true,
    };

    // View-only permissions (employees can view but not modify)
    const viewOnly: RolePermissions = {
      ...basePermissions,
      canAdd: false,
      canEdit: false,
      canDelete: false,
    };

    // Admin-only permissions
    const adminOnly: RolePermissions = {
      ...basePermissions,
      canAdd: isAdmin,
      canEdit: isAdmin,
      canDelete: isAdmin,
    };

    return {
      // Products: Employees can view, export, refresh but not add/edit
      products: {
        ...basePermissions,
        canAdd: isAdmin,
        canEdit: isAdmin,
        canDelete: isAdmin,
      },

      // Orders: Full access for both roles
      orders: fullAccess,

      // Customers: Full access for both roles
      customers: fullAccess,

      // Inventory: Employees can view but not add/edit
      inventory: {
        ...basePermissions,
        canAdd: isAdmin,
        canEdit: isAdmin,
        canDelete: isAdmin,
      },

      // Consultant: Full access for both roles
      consultant: fullAccess,

      // Coupons: Employees can view but not create/edit
      coupons: {
        ...basePermissions,
        canAdd: isAdmin,
        canEdit: isAdmin,
        canDelete: isAdmin,
      },

      // Staff: Employees can view but not manage
      staff: {
        ...basePermissions,
        canAdd: isAdmin,
        canEdit: isAdmin,
        canDelete: isAdmin,
      },

      // Calendar: Full access for both roles
      calendar: fullAccess,

      // Feedback: Full access for both roles
      feedback: fullAccess,

      // Reviews: Full access for both roles
      reviews: fullAccess,

      // Returns: Full access for both roles
      returns: fullAccess,

      // Notifications: Full access for both roles
      notifications: fullAccess,

      // Profile: Employees can view but not edit (except own profile photo)
      profile: {
        ...basePermissions,
        canEdit: isAdmin, // Only admins can edit profile information
      },
    };
  }, [userRole, isAdmin, isEmployee]);

  return permissions;
};

export default useRolePermissions;
