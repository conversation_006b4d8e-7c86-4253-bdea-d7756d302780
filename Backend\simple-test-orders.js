const mongoose = require('mongoose');
require('dotenv').config();

async function createSimpleTestOrders() {
    try {
        // Connect to database
        await mongoose.connect(process.env.DB_URL, {
            dbName: process.env.DB_NAME || 'ecommerce',
        });
        console.log('✅ Connected to MongoDB');

        // Define Order schema inline for simplicity
        const orderSchema = new mongoose.Schema({
            user: { type: mongoose.Schema.Types.ObjectId, ref: 'User' },
            items: [{
                product: { type: mongoose.Schema.Types.ObjectId, ref: 'Product' },
                quantity: { type: Number, default: 1 },
                price: { type: Number, default: 100 }
            }],
            totalAmount: { type: Number, default: 100 },
            status: { type: String, default: 'pending' },
            shippingAddress: {
                addressLine1: String,
                city: String,
                state: String,
                postalCode: String,
                country: { type: String, default: 'India' }
            },
            createdAt: { type: Date, default: Date.now }
        });

        const Order = mongoose.models.Order || mongoose.model('Order', orderSchema);

        // Check existing orders
        const existingOrders = await Order.find({});
        console.log(`📊 Found ${existingOrders.length} existing orders`);

        if (existingOrders.length > 0) {
            console.log('✅ Orders already exist. Displaying existing orders:');
            existingOrders.forEach((order, index) => {
                console.log(`   ${index + 1}. Order ${order._id} - Status: ${order.status} - Amount: ₹${order.totalAmount}`);
            });
            return;
        }

        // Get a user to associate with orders
        const User = mongoose.model('User');
        const users = await User.find({});
        
        if (users.length === 0) {
            console.log('❌ No users found. Cannot create orders without users.');
            return;
        }

        const testUser = users[0];
        console.log(`👤 Using user: ${testUser.name} (${testUser.email})`);

        // Create simple test orders
        const testOrders = [
            {
                user: testUser._id,
                items: [{
                    quantity: 2,
                    price: 150
                }],
                totalAmount: 300,
                status: 'pending',
                shippingAddress: {
                    addressLine1: '123 Test Street',
                    city: 'Mumbai',
                    state: 'Maharashtra',
                    postalCode: '400001',
                    country: 'India'
                }
            },
            {
                user: testUser._id,
                items: [{
                    quantity: 1,
                    price: 250
                }],
                totalAmount: 250,
                status: 'processing',
                shippingAddress: {
                    addressLine1: '456 Sample Road',
                    city: 'Delhi',
                    state: 'Delhi',
                    postalCode: '110001',
                    country: 'India'
                }
            },
            {
                user: testUser._id,
                items: [{
                    quantity: 3,
                    price: 100
                }],
                totalAmount: 300,
                status: 'shipped',
                shippingAddress: {
                    addressLine1: '789 Demo Lane',
                    city: 'Bangalore',
                    state: 'Karnataka',
                    postalCode: '560001',
                    country: 'India'
                }
            }
        ];

        // Insert orders
        const createdOrders = await Order.insertMany(testOrders);
        console.log(`✅ Created ${createdOrders.length} test orders successfully!`);

        // Display created orders
        createdOrders.forEach((order, index) => {
            console.log(`   ${index + 1}. Order ${order._id} - Status: ${order.status} - Amount: ₹${order.totalAmount}`);
        });

        console.log('\n💡 Test orders created! You can now test the Orders page.');

    } catch (error) {
        console.error('❌ Error:', error.message);
    } finally {
        await mongoose.disconnect();
        console.log('🔌 Disconnected from MongoDB');
    }
}

createSimpleTestOrders();
