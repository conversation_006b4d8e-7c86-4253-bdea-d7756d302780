require('dotenv').config();
const { sendTwoFactorEmail } = require('./utils/sendEmail');

async function testEmailService() {
    console.log('🧪 Testing Email Service...\n');

    // Check environment variables
    console.log('📧 Email Configuration:');
    console.log('   EMAIL_USER:', process.env.EMAIL_USER ? '✅ Set' : '❌ Not set');
    console.log('   EMAIL_PASS:', process.env.EMAIL_PASS ? '✅ Set' : '❌ Not set');
    console.log('   EMAIL_HOST:', process.env.EMAIL_HOST || 'Not set');
    console.log('   EMAIL_PORT:', process.env.EMAIL_PORT || 'Not set');

    if (!process.env.EMAIL_USER || !process.env.EMAIL_PASS) {
        console.log('\n❌ Email configuration is incomplete. Please check your .env file.');
        console.log('   Required variables: EMAIL_USER, EMAIL_PASS');
        return;
    }

    try {
        console.log('\n📤 Sending test email...');
        
        // Generate a test code
        const testCode = Math.floor(100000 + Math.random() * 900000).toString();
        
        // Send test email to the configured email address (self-test)
        const result = await sendTwoFactorEmail(
            process.env.EMAIL_USER, // Send to self for testing
            testCode,
            'Test User'
        );

        if (result) {
            console.log('✅ Test email sent successfully!');
            console.log(`📱 Test code: ${testCode}`);
            console.log(`📧 Sent to: ${process.env.EMAIL_USER}`);
            console.log('\n💡 Check your email inbox for the verification code.');
        } else {
            console.log('❌ Failed to send test email.');
        }

    } catch (error) {
        console.error('❌ Email service test failed:', error.message);
        
        // Provide specific error guidance
        if (error.message.includes('Invalid login')) {
            console.log('\n💡 Troubleshooting:');
            console.log('   - Check if EMAIL_USER and EMAIL_PASS are correct');
            console.log('   - For Gmail, use App Password instead of regular password');
            console.log('   - Enable 2-factor authentication on Gmail and generate App Password');
        } else if (error.message.includes('ENOTFOUND')) {
            console.log('\n💡 Troubleshooting:');
            console.log('   - Check your internet connection');
            console.log('   - Verify EMAIL_HOST is correct (smtp.gmail.com for Gmail)');
        }
    }
}

// Run the test
testEmailService();
