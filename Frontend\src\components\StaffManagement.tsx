import { useState, useEffect, use<PERSON>emo } from "react";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { useToast } from "@/hooks/use-toast";
import { usePagination } from "@/hooks/use-pagination";
import { TablePagination } from "@/components/ui/table-pagination";
import { Plus, Edit, Trash2, User, RefreshCw } from "lucide-react";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import AnimatedNumber from "./AnimatedNumber";
import AnimatedText from "./AnimatedText";
import AddStaffModal from "./AddStaffModal";
import EditStaffModal from "./EditStaffModal";
import { useProfile } from "@/contexts/ProfileContext";
import authAPI from "@/api/auth.api.js";
import staffAPI from "@/api/staff.api.js";
import { apiHelpers } from "@/api";

// Interface for staff member
interface StaffMember {
  _id: string;
  name: string;
  email: string;
  phone: string;
  position?: string;
  department: string;
  role: "admin" | "employee";
  status: "active" | "inactive" | "banned";
  joinedDate?: string;
  createdAt: string;
}

const StaffManagement = () => {
  const { toast } = useToast();
  const { profileData } = useProfile();

  // State management
  const [staff, setStaff] = useState<StaffMember[]>([]);
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [isAddModalOpen, setIsAddModalOpen] = useState(false);
  const [isEditModalOpen, setIsEditModalOpen] = useState(false);
  const [selectedStaff, setSelectedStaff] = useState<StaffMember | null>(null);
  const [searchTerm, setSearchTerm] = useState("");
  const [statusFilter, setStatusFilter] = useState("all");
  const [roleFilter, setRoleFilter] = useState("all");
  const [stats, setStats] = useState({
    total: 0,
    active: 0,
    inactive: 0,
    departmentCount: 0,
  });

  // Get current user role for access control
  const currentUser = authAPI.getCurrentUser();
  const isAdmin = authAPI.isAdmin();
  const canManageStaff = isAdmin; // Only admins can manage staff

  // Check if a staff member is the current user
  const isCurrentUser = (memberEmail: string) => {
    return memberEmail === profileData.email;
  };

  // Fetch staff data from API
  const fetchStaff = async (showLoading = true) => {
    try {
      if (showLoading) setLoading(true);

      const filters = {
        search: searchTerm,
        status: statusFilter !== "all" ? statusFilter : "",
        role: roleFilter !== "all" ? roleFilter : "",
        limit: 100, // Get all staff for now
      };

      const response = await staffAPI.getStaff(filters);

      if (apiHelpers.isSuccessResponse(response)) {
        const data = apiHelpers.extractData(response);
        setStaff(data.staff || []);

        if (data.stats) {
          setStats({
            total: data.stats.total || 0,
            active: data.stats.active || 0,
            inactive: data.stats.inactive || 0,
            departmentCount: data.stats.departmentCount || 0,
          });
        }

        console.log("Staff data loaded:", data);
      } else {
        throw new Error(response.message || "Failed to fetch staff");
      }
    } catch (error: any) {
      console.error("Failed to fetch staff:", error);
      toast({
        title: "Error",
        description: apiHelpers.formatError(error),
        variant: "destructive",
      });
    } finally {
      setLoading(false);
      setRefreshing(false);
    }
  };

  // Refresh staff data
  const handleRefresh = async () => {
    setRefreshing(true);
    await fetchStaff(false);
  };

  // Clean up admin users (remove all except 'priyanshu')
  const handleAdminCleanup = async () => {
    try {
      const adminUsersToRemove = staff.filter((member) => {
        return (
          member.role === "admin" &&
          member.email !== "<EMAIL>" &&
          !member.name?.toLowerCase().includes("priyanshu") &&
          !member.email?.toLowerCase().includes("priyanshu")
        );
      });

      if (adminUsersToRemove.length === 0) {
        toast({
          title: "No Cleanup Needed",
          description: "No admin users to remove",
        });
        return;
      }

      // Remove each admin user
      for (const user of adminUsersToRemove) {
        try {
          await staffAPI.deleteStaff(user._id);
          console.log(`Removed admin user: ${user.name} (${user.email})`);
        } catch (error) {
          console.error(`Failed to remove admin user ${user.name}:`, error);
        }
      }

      toast({
        title: "Admin Cleanup Complete",
        description: `Removed ${adminUsersToRemove.length} admin user(s). Only 'priyanshu' admin account remains.`,
      });

      // Refresh staff data
      await fetchStaff(false);
    } catch (error: any) {
      console.error("Failed to cleanup admin users:", error);
      toast({
        title: "Error",
        description: "Failed to cleanup admin users",
        variant: "destructive",
      });
    }
  };

  // Load staff data on component mount and when filters change
  useEffect(() => {
    if (canManageStaff) {
      fetchStaff();
    } else {
      setLoading(false);
    }
  }, [canManageStaff, searchTerm, statusFilter, roleFilter]);

  // Auto-cleanup dummy admin users on component mount
  useEffect(() => {
    const autoCleanupAdmins = async () => {
      if (canManageStaff && staff.length > 0) {
        // Check if there are dummy admin users to remove
        const adminUsersToRemove = staff.filter((member) => {
          return (
            member.role === "admin" &&
            member.email !== "<EMAIL>" &&
            !member.name?.toLowerCase().includes("priyanshu") &&
            !member.email?.toLowerCase().includes("priyanshu")
          );
        });

        if (adminUsersToRemove.length > 0) {
          console.log(
            `Auto-cleaning ${adminUsersToRemove.length} dummy admin users...`
          );
          await handleAdminCleanup();
        }
      }
    };

    // Run cleanup after staff data is loaded
    if (!loading && staff.length > 0) {
      autoCleanupAdmins();
    }
  }, [staff, loading, canManageStaff]);

  const clearFilters = () => {
    setSearchTerm("");
    setStatusFilter("all");
    setRoleFilter("all");
  };

  // Filter staff to only show valid users (remove admin users except 'priyanshu')
  const filteredStaff = useMemo(() => {
    if (!staff || !Array.isArray(staff)) return [];

    return staff.filter((member) => {
      // Keep all employee users
      if (member.role === "employee") return true;

      // For admin users, only keep 'priyanshu'
      if (member.role === "admin") {
        return (
          member.email === "<EMAIL>" ||
          member.name?.toLowerCase().includes("priyanshu") ||
          member.email?.toLowerCase().includes("priyanshu")
        );
      }

      return true;
    });
  }, [staff]);

  // Pagination logic
  const {
    currentPage,
    totalPages,
    paginatedData: paginatedStaff,
    goToPage,
    totalItems,
  } = usePagination({
    data: filteredStaff,
    itemsPerPage: 10,
  });

  // Handle status change
  const handleStatusChange = async (id: string, newStatus: string) => {
    try {
      const response = await staffAPI.updateStaffStatus(id, newStatus);

      if (apiHelpers.isSuccessResponse(response)) {
        toast({
          title: "Status Updated",
          description: `Staff member status changed to ${newStatus}`,
        });

        // Refresh staff data
        await fetchStaff(false);
      } else {
        throw new Error(response.message || "Failed to update status");
      }
    } catch (error: any) {
      console.error("Failed to update staff status:", error);
      toast({
        title: "Error",
        description: apiHelpers.formatError(error),
        variant: "destructive",
      });
    }
  };

  // Handle status toggle
  const handleStatusToggle = async (id: string, currentStatus: string) => {
    const statusCycle = ["active", "inactive"];
    const currentIndex = statusCycle.indexOf(currentStatus.toLowerCase());
    const nextStatus = statusCycle[(currentIndex + 1) % statusCycle.length];
    await handleStatusChange(id, nextStatus);
  };

  // Handle add staff (send invitation)
  const handleAddStaff = async (newStaffData: any) => {
    try {
      const response = await staffAPI.inviteStaff(newStaffData);

      if (apiHelpers.isSuccessResponse(response)) {
        toast({
          title: "Staff Invitation Sent! 📧",
          description: `Invitation email sent to ${newStaffData.email}. ${newStaffData.name} will receive instructions to set up their password and access the admin panel.`,
        });

        // Refresh staff data
        await fetchStaff(false);
        setIsAddModalOpen(false);
      } else {
        throw new Error(response.message || "Failed to send staff invitation");
      }
    } catch (error: any) {
      console.error("Failed to invite staff:", error);

      // Check if it's a duplicate email error
      if (error.message && error.message.includes("already exists")) {
        toast({
          title: "Email Already Registered",
          description:
            "A user with this email address already exists in the system.",
          variant: "destructive",
        });
      } else {
        toast({
          title: "Invitation Failed",
          description: apiHelpers.formatError(error),
          variant: "destructive",
        });
      }
    }
  };

  // Handle edit staff
  const handleEditStaff = (staffMember: StaffMember) => {
    setSelectedStaff(staffMember);
    setIsEditModalOpen(true);
  };

  // Handle update staff
  const handleUpdateStaff = async (updatedStaffData: any) => {
    try {
      if (!selectedStaff) return;

      const response = await staffAPI.updateStaff(
        selectedStaff._id,
        updatedStaffData
      );

      if (apiHelpers.isSuccessResponse(response)) {
        toast({
          title: "Staff Updated",
          description: `${updatedStaffData.name} has been updated`,
        });

        // Refresh staff data
        await fetchStaff(false);
        setIsEditModalOpen(false);
        setSelectedStaff(null);
      } else {
        throw new Error(response.message || "Failed to update staff member");
      }
    } catch (error: any) {
      console.error("Failed to update staff:", error);
      toast({
        title: "Error",
        description: apiHelpers.formatError(error),
        variant: "destructive",
      });
    }
  };

  // Handle delete staff
  const handleDeleteStaff = async (id: string, name: string) => {
    try {
      const response = await staffAPI.deleteStaff(id);

      if (apiHelpers.isSuccessResponse(response)) {
        toast({
          title: "Staff Deleted",
          description: `${name} has been removed from staff`,
        });

        // Refresh staff data
        await fetchStaff(false);
      } else {
        throw new Error(response.message || "Failed to delete staff member");
      }
    } catch (error: any) {
      console.error("Failed to delete staff:", error);
      toast({
        title: "Error",
        description: apiHelpers.formatError(error),
        variant: "destructive",
      });
    }
  };

  const getStatusColor = (status: string) => {
    switch (status.toLowerCase()) {
      case "active":
        return "bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200";
      case "inactive":
        return "bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200";
      case "banned":
        return "bg-orange-100 text-orange-800 dark:bg-orange-900 dark:text-orange-200";
      default:
        return "bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200";
    }
  };

  // Format status for display
  const formatStatus = (status: string) => {
    return status.charAt(0).toUpperCase() + status.slice(1);
  };

  // Stats for display
  const displayStats = [
    {
      title: "Total Staff",
      value: stats.total.toString(),
      color: "text-gray-900",
    },
    {
      title: "Active",
      value: stats.active.toString(),
      color: "text-green-600",
    },
    {
      title: "Inactive",
      value: stats.inactive.toString(),
      color: "text-red-600",
    },
    {
      title: "Departments",
      value: stats.departmentCount.toString(),
      color: "text-blue-600",
    },
  ];

  // Show access denied message for non-admin users
  if (!canManageStaff) {
    return (
      <div className="flex items-center justify-center min-h-[400px]">
        <div className="text-center">
          <h2 className="text-2xl font-bold text-gray-900 dark:text-white mb-4">
            Access Denied
          </h2>
          <p className="text-gray-600 dark:text-gray-400">
            You don't have permission to access staff management.
          </p>
          <p className="text-sm text-gray-500 dark:text-gray-500 mt-2">
            Only administrators can manage staff members.
          </p>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6 transition-colors duration-500 ease-in-out">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-gray-900 dark:text-white transition-colors duration-500 ease-in-out">
            Staff Management
          </h1>
          <p className="text-base text-gray-600 dark:text-gray-300 transition-colors duration-500 ease-in-out">
            Manage team members and their access permissions
          </p>
        </div>
        <div className="flex items-center space-x-2">
          <Button
            onClick={handleAdminCleanup}
            variant="outline"
            className="bg-orange-50 hover:bg-orange-100 border-orange-200 text-orange-700 dark:bg-orange-900/20 dark:hover:bg-orange-900/30 dark:border-orange-800 dark:text-orange-300"
            disabled={loading}
          >
            <Trash2 className="w-4 h-4 mr-2" />
            Cleanup Admins
          </Button>
          <Button
            onClick={handleRefresh}
            variant="outline"
            disabled={refreshing}
            className="flex items-center"
          >
            <RefreshCw
              className={`w-4 h-4 mr-2 ${refreshing ? "animate-spin" : ""}`}
            />
            Refresh
          </Button>
          <Button
            onClick={() => setIsAddModalOpen(true)}
            className="bg-green-600 hover:bg-green-700 text-white"
            disabled={loading}
          >
            <Plus className="w-4 h-4 mr-2" />
            Add Staff
          </Button>
        </div>
      </div>

      {/* Staff Statistics */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        {displayStats.map((stat, index) => (
          <Card
            key={index}
            className="transition-colors duration-500 ease-in-out bg-white dark:bg-gray-800 border-gray-200 dark:border-gray-700"
          >
            <CardContent className="p-6">
              <div className="space-y-2">
                <p className="text-lg font-medium text-gray-600 dark:text-gray-400 transition-colors duration-500 ease-in-out">
                  {stat.title}
                </p>
                <AnimatedNumber
                  value={loading ? "..." : stat.value}
                  className="text-3xl font-bold"
                />
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      {/* Search and Filters */}
      <Card className="transition-colors duration-500 ease-in-out bg-white dark:bg-gray-800 border-gray-200 dark:border-gray-700">
        <CardContent className="p-6">
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-2 transition-colors duration-500 ease-in-out text-gray-700 dark:text-gray-300">
                <span className="font-medium text-xl">
                  🔍 Search & Filter Team
                </span>
              </div>
              <Button
                variant="outline"
                onClick={clearFilters}
                className="text-base"
              >
                Clear Filters
              </Button>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div>
                <label className="block text-lg font-medium transition-colors duration-500 ease-in-out text-gray-700 dark:text-gray-300 mb-2">
                  Search Team
                </label>
                <Input
                  placeholder="Search by name, email, position, or department..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="transition-colors duration-500 ease-in-out bg-white dark:bg-gray-700 border-gray-300 dark:border-gray-600 text-base"
                />
              </div>
              <div>
                <label className="block text-base font-medium transition-colors duration-500 ease-in-out text-gray-700 dark:text-gray-300 mb-2">
                  Role
                </label>
                <Select value={roleFilter} onValueChange={setRoleFilter}>
                  <SelectTrigger className="text-base">
                    <SelectValue placeholder="All Roles" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All Roles</SelectItem>
                    <SelectItem value="admin">Admin</SelectItem>
                    <SelectItem value="support">Support</SelectItem>
                    <SelectItem value="manager">Manager</SelectItem>
                    <SelectItem value="specialist">Specialist</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              <div>
                <label className="block text-base font-medium transition-colors duration-500 ease-in-out text-gray-700 dark:text-gray-300 mb-2">
                  Status
                </label>
                <Select value={statusFilter} onValueChange={setStatusFilter}>
                  <SelectTrigger className="text-base">
                    <SelectValue placeholder="All Status" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All</SelectItem>
                    <SelectItem value="active">Active</SelectItem>
                    <SelectItem value="inactive">Inactive</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Staff Table */}
      <Card className="transition-colors duration-500 ease-in-out bg-white dark:bg-gray-800 border-gray-200 dark:border-gray-700">
        <CardHeader>
          <CardTitle className="text-gray-900 dark:text-white transition-colors duration-500 ease-in-out">
            Team Members
          </CardTitle>
        </CardHeader>
        <CardContent>
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead className="text-lg font-medium transition-colors duration-500 ease-in-out text-gray-700 dark:text-gray-300 text-center">
                  Staff Details
                </TableHead>
                <TableHead className="text-lg font-medium transition-colors duration-500 ease-in-out text-gray-700 dark:text-gray-300 text-center">
                  Position
                </TableHead>
                <TableHead className="text-lg font-medium transition-colors duration-500 ease-in-out text-gray-700 dark:text-gray-300 text-center">
                  Department
                </TableHead>
                <TableHead className="text-lg font-medium transition-colors duration-500 ease-in-out text-gray-700 dark:text-gray-300 text-center">
                  Join Date
                </TableHead>
                <TableHead className="text-lg font-medium transition-colors duration-500 ease-in-out text-gray-700 dark:text-gray-300 text-center">
                  Status
                </TableHead>
                <TableHead className="text-lg font-medium transition-colors duration-500 ease-in-out text-gray-700 dark:text-gray-300 text-center">
                  Actions
                </TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {loading ? (
                // Loading skeleton
                Array.from({ length: 5 }).map((_, index) => (
                  <TableRow key={index}>
                    <TableCell className="text-center">
                      <div className="flex items-center justify-center gap-3">
                        <div className="w-10 h-10 rounded-full bg-gray-200 dark:bg-gray-700 animate-pulse"></div>
                        <div className="flex flex-col space-y-2">
                          <div className="h-4 w-32 bg-gray-200 dark:bg-gray-700 rounded animate-pulse"></div>
                          <div className="h-3 w-40 bg-gray-200 dark:bg-gray-700 rounded animate-pulse"></div>
                          <div className="h-3 w-28 bg-gray-200 dark:bg-gray-700 rounded animate-pulse"></div>
                        </div>
                      </div>
                    </TableCell>
                    <TableCell className="text-center">
                      <div className="h-4 w-24 bg-gray-200 dark:bg-gray-700 rounded animate-pulse mx-auto"></div>
                    </TableCell>
                    <TableCell className="text-center">
                      <div className="h-6 w-20 bg-gray-200 dark:bg-gray-700 rounded animate-pulse mx-auto"></div>
                    </TableCell>
                    <TableCell className="text-center">
                      <div className="h-4 w-20 bg-gray-200 dark:bg-gray-700 rounded animate-pulse mx-auto"></div>
                    </TableCell>
                    <TableCell className="text-center">
                      <div className="h-8 w-20 bg-gray-200 dark:bg-gray-700 rounded animate-pulse mx-auto"></div>
                    </TableCell>
                    <TableCell className="text-center">
                      <div className="flex items-center justify-center gap-2">
                        <div className="h-8 w-8 bg-gray-200 dark:bg-gray-700 rounded animate-pulse"></div>
                        <div className="h-8 w-8 bg-gray-200 dark:bg-gray-700 rounded animate-pulse"></div>
                      </div>
                    </TableCell>
                  </TableRow>
                ))
              ) : paginatedStaff.length === 0 ? (
                // Empty state
                <TableRow>
                  <TableCell colSpan={6} className="text-center py-8">
                    <div className="flex flex-col items-center justify-center space-y-3">
                      <User className="w-12 h-12 text-gray-400" />
                      <p className="text-lg font-medium text-gray-500">
                        No staff members found
                      </p>
                      <p className="text-sm text-gray-400">
                        {searchTerm ||
                        statusFilter !== "all" ||
                        roleFilter !== "all"
                          ? "Try adjusting your filters"
                          : "Add your first staff member to get started"}
                      </p>
                    </div>
                  </TableCell>
                </TableRow>
              ) : (
                // Staff data
                paginatedStaff.map((member) => (
                  <TableRow key={member._id}>
                    <TableCell className="text-center">
                      <div className="flex items-center justify-center gap-3">
                        <div className="w-10 h-10 rounded-full overflow-hidden border-2 border-gray-200 dark:border-gray-600 bg-white dark:bg-gray-800">
                          {isCurrentUser(member.email) &&
                          profileData.profileImage ? (
                            <img
                              src={profileData.profileImage}
                              alt={member.name}
                              className="w-full h-full object-cover object-center"
                            />
                          ) : (
                            <div className="w-full h-full bg-gray-400 dark:bg-gray-600 rounded-full flex items-center justify-center">
                              <User className="w-5 h-5 text-white" />
                            </div>
                          )}
                        </div>
                        <div className="flex flex-col">
                          <AnimatedText className="font-medium text-lg">
                            {member.name}
                          </AnimatedText>
                          <span className="text-base text-gray-500 dark:text-gray-400 transition-colors duration-500 ease-in-out">
                            {member.email}
                          </span>
                          <span className="text-base text-gray-500 dark:text-gray-400 transition-colors duration-500 ease-in-out">
                            {member.phone}
                          </span>
                        </div>
                      </div>
                    </TableCell>
                    <TableCell className="text-center">
                      <div className="flex flex-col items-center">
                        <span className="font-medium text-lg text-gray-900 dark:text-white transition-colors duration-500 ease-in-out">
                          {member.position || member.role}
                        </span>
                        <Badge variant="secondary" className="text-xs mt-1">
                          {member.role}
                        </Badge>
                      </div>
                    </TableCell>
                    <TableCell className="text-center">
                      <Badge variant="outline" className="text-base">
                        {member.department}
                      </Badge>
                    </TableCell>
                    <TableCell className="text-center text-lg text-gray-600 dark:text-gray-400 transition-colors duration-500 ease-in-out">
                      {member.joinedDate
                        ? new Date(member.joinedDate).toLocaleDateString()
                        : new Date(member.createdAt).toLocaleDateString()}
                    </TableCell>
                    <TableCell className="text-center">
                      <Button
                        variant="outline"
                        onClick={() =>
                          handleStatusToggle(member._id, member.status)
                        }
                        className="w-full max-w-[140px] text-base"
                        disabled={refreshing}
                      >
                        <Badge
                          className={`${getStatusColor(
                            member.status
                          )} text-base`}
                        >
                          {formatStatus(member.status)}
                        </Badge>
                      </Button>
                    </TableCell>
                    <TableCell className="text-center">
                      <div className="flex items-center justify-center gap-2">
                        {/* Edit button - only for admins */}
                        <Button
                          variant="ghost"
                          size="sm"
                          className="p-2 hover:bg-blue-50 dark:hover:bg-blue-900"
                          onClick={() => handleEditStaff(member)}
                          disabled={refreshing}
                        >
                          <Edit className="w-4 h-4 text-blue-600 dark:text-blue-400" />
                        </Button>
                        {/* Delete button - only for admins and not current user */}
                        {!isCurrentUser(member.email) && (
                          <Button
                            variant="ghost"
                            size="sm"
                            className="p-2 hover:bg-red-50 dark:hover:bg-red-900"
                            onClick={() => {
                              handleDeleteStaff(member._id, member.name);
                            }}
                            disabled={refreshing}
                          >
                            <Trash2 className="w-4 h-4 text-red-600 dark:text-red-400" />
                          </Button>
                        )}
                      </div>
                    </TableCell>
                  </TableRow>
                ))
              )}
            </TableBody>
          </Table>

          {/* Pagination */}
          <TablePagination
            currentPage={currentPage}
            totalPages={totalPages}
            onPageChange={goToPage}
            totalItems={totalItems}
            itemsPerPage={10}
          />
        </CardContent>
      </Card>

      <AddStaffModal
        isOpen={isAddModalOpen}
        onClose={() => setIsAddModalOpen(false)}
        onAdd={handleAddStaff}
      />

      <EditStaffModal
        isOpen={isEditModalOpen}
        onClose={() => {
          setIsEditModalOpen(false);
          setSelectedStaff(null);
        }}
        staff={selectedStaff}
        onUpdate={handleUpdateStaff}
      />
    </div>
  );
};

export default StaffManagement;
