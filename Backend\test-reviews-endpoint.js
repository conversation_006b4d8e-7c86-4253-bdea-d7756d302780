const axios = require('axios');

const BASE_URL = 'http://localhost:5000';

async function testReviewsEndpoint() {
    console.log('🧪 Testing Reviews Endpoint...\n');

    try {
        // Test 1: Check if reviews endpoint exists (without auth)
        console.log('1. Testing reviews endpoint without authentication...');
        try {
            const response = await axios.get(`${BASE_URL}/api/admin/reviews`);
            console.log('❌ Reviews endpoint should require authentication');
        } catch (error) {
            if (error.response && error.response.status === 401) {
                console.log('✅ Reviews endpoint properly requires authentication');
                console.log('   Status:', error.response.status);
                console.log('   Message:', error.response.data?.message);
            } else {
                console.log('❓ Unexpected error:', error.message);
            }
        }

        // Test 2: Test with mock token to see endpoint structure
        console.log('\n2. Testing reviews endpoint structure...');
        try {
            const response = await axios.get(`${BASE_URL}/api/admin/reviews`, {
                headers: {
                    'Authorization': 'Bearer mock-token'
                }
            });
            console.log('✅ Reviews endpoint response:', response.data);
        } catch (error) {
            if (error.response) {
                console.log('📋 Reviews endpoint error (expected due to auth):');
                console.log('   Status:', error.response.status);
                console.log('   Message:', error.response.data?.message);
                
                // Check if it's a token validation error (good) vs route not found (bad)
                if (error.response.status === 401 || error.response.status === 403) {
                    console.log('✅ Endpoint exists and is properly protected');
                } else if (error.response.status === 404) {
                    console.log('❌ Endpoint not found - route may not be configured');
                }
            } else {
                console.log('❌ Network error:', error.message);
            }
        }

        // Test 3: Test stats endpoint
        console.log('\n3. Testing reviews stats endpoint...');
        try {
            const response = await axios.get(`${BASE_URL}/api/admin/reviews/stats`, {
                headers: {
                    'Authorization': 'Bearer mock-token'
                }
            });
            console.log('✅ Reviews stats endpoint response:', response.data);
        } catch (error) {
            if (error.response) {
                console.log('📋 Reviews stats endpoint error:');
                console.log('   Status:', error.response.status);
                console.log('   Message:', error.response.data?.message);
                
                if (error.response.status === 401 || error.response.status === 403) {
                    console.log('✅ Stats endpoint exists and is properly protected');
                } else if (error.response.status === 404) {
                    console.log('❌ Stats endpoint not found');
                }
            } else {
                console.log('❌ Network error:', error.message);
            }
        }

        console.log('\n💡 Reviews endpoint testing completed');

    } catch (error) {
        console.error('❌ Test failed:', error.message);
    }
}

// Run the test
testReviewsEndpoint();
