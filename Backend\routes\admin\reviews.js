const express = require('express');
const router = express.Router();
const {
    getAllReviews,
    updateReviewStatus,
    deleteReview,
    getReviewStats,
    getReviewById
} = require('../../controllers/reviewController');
const { protect, authorize } = require('../../middleware/auth');

// All routes are protected and require admin/employee access
router.use(protect);
router.use(authorize('admin', 'employee'));

// @route   GET /api/admin/reviews
// @desc    Get all reviews (admin)
// @access  Private/Admin
router.get('/', getAllReviews);

// @route   GET /api/admin/reviews/stats
// @desc    Get review statistics
// @access  Private/Admin
router.get('/stats', getReviewStats);

// @route   GET /api/admin/reviews/:id
// @desc    Get review by ID
// @access  Private/Admin
router.get('/:id', getReviewById);

// @route   PUT /api/admin/reviews/:id/status
// @desc    Update review status (approve/reject)
// @access  Private/Admin
router.put('/:id/status', updateReviewStatus);

// @route   DELETE /api/admin/reviews/:id
// @desc    Delete review
// @access  Private/Admin
router.delete('/:id', deleteReview);

module.exports = router;
