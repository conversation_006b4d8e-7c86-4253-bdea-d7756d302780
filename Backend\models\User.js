const mongoose = require("mongoose");

const userSchema = new mongoose.Schema(
  {
    name: { type: String, required: true },
    email: { type: String, required: true, unique: true },
    password: { type: String, select: false },
    isPasswordSet: { type: Boolean, default: false },
    role: {
      type: String,
      enum: ["user", "admin", "employee"],
      default: "user",
    },
    department: { type: String },
    joinedDate: { type: Date },
    phone: {
      type: String,
      unique: true,
      sparse: true,
      validate: {
        validator: function (v) {
          // Allow empty phone numbers for staff invitations
          if (!v || v === "") return true;
          // Remove all non-digit characters and check if it's 10 digits
          const cleanPhone = v.replace(/\D/g, "");
          return cleanPhone.length >= 10 && cleanPhone.length <= 15;
        },
        message: "Phone number must be between 10-15 digits",
      },
    },
    status: {
      type: String,
      enum: ["active", "inactive", "banned"],
      default: "active",
    },
    shippingAddress: [
      {
        fullName: { type: String, required: true },
        address: { type: String, required: true },
        city: { type: String, required: true },
        postalCode: { type: String, required: true },
        country: { type: String, required: true },
      },
    ],
    profileImage: {
      url: { type: String, default: "" },
      publicId: { type: String, default: "" },
    },
    paymentMethod: { type: String, required: true },
    isEmailVerified: { type: Boolean, default: false },
    emailVerificationToken: { type: String },
    emailVerificationExpires: { type: Date },
    passwordResetToken: { type: String },
    passwordResetExpires: { type: Date },
    twoFactorCode: { type: String },
    twoFactorExpires: { type: Date },
    isTwoFactorEnabled: { type: Boolean, default: true },
    createdAt: { type: Date, default: Date.now },
    updatedAt: { type: Date, default: Date.now },
  },
  {
    timestamps: true,
  }
);
const User = mongoose.model("User", userSchema);

userSchema.pre("save", function (next) {
  if (!this.isNew) {
    this.updatedAt = Date.now();
  }
  next();
});

userSchema.pre("findOneAndUpdate", function (next) {
  this.set({ updatedAt: Date.now() });
  next();
});

userSchema.pre("updateOne", function (next) {
  this.set({ updatedAt: Date.now() });
  next();
});

userSchema.pre("updateMany", function (next) {
  this.set({ updatedAt: Date.now() });
  next();
});

module.exports = User;
