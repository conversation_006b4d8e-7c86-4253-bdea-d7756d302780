require('dotenv').config();
const mongoose = require('mongoose');
const Notification = require('./models/Notification');
const User = require('./models/User');

async function createEmployeeNotifications() {
  try {
    // Connect to MongoDB
    await mongoose.connect(process.env.DB_URL + process.env.DB_NAME);
    console.log('✅ Connected to MongoDB');

    // Find employee user
    const employeeUser = await User.findOne({ email: '<EMAIL>' });
    if (!employeeUser) {
      console.log('❌ Employee user not found. Please create employee user first.');
      return;
    }

    console.log('👤 Found employee user:', employeeUser.name);

    // Clear existing notifications for employee
    await Notification.deleteMany({ user: employeeUser._id });
    console.log('🗑️ Cleared existing employee notifications');

    // Employee-appropriate notifications
    const employeeNotifications = [
      {
        user: employeeUser._id,
        title: 'Welcome to Dr. <PERSON> Admin Panel',
        message: 'Welcome to the Dr. <PERSON> Laboratories Admin Panel! You have access to view and manage laboratory operations.',
        type: 'success',
        priority: 'high',
        category: 'system',
        targetRoles: ['admin', 'employee']
      },
      {
        user: employeeUser._id,
        title: 'New Order Received',
        message: 'A new order #ORD-2024-003 has been placed by Jane Smith for ₹1,800.',
        type: 'info',
        priority: 'medium',
        category: 'updates',
        targetRoles: ['admin', 'employee'],
        data: { orderId: 'ORD-2024-003', customerName: 'Jane Smith', amount: 1800 }
      },
      {
        user: employeeUser._id,
        title: 'Customer Feedback Received',
        message: 'New 4-star review received from customer Mike Johnson: "Good service, timely delivery!"',
        type: 'info',
        priority: 'low',
        category: 'updates',
        targetRoles: ['admin', 'employee'],
        data: { customerName: 'Mike Johnson', rating: 4, review: 'Good service, timely delivery!' }
      },
      {
        user: employeeUser._id,
        title: 'Monthly Report Available',
        message: 'Your monthly analytics report for January 2024 is now available for download.',
        type: 'report',
        priority: 'medium',
        category: 'reports',
        targetRoles: ['admin', 'employee'],
        data: { reportType: 'monthly', month: 'January', year: 2024 }
      },
      {
        user: employeeUser._id,
        title: 'Payment Received',
        message: 'Payment of ₹2,200 received for order #ORD-2024-004 from customer Lisa Davis.',
        type: 'success',
        priority: 'medium',
        category: 'updates',
        targetRoles: ['admin', 'employee'],
        data: { orderId: 'ORD-2024-004', amount: 2200, customerName: 'Lisa Davis' }
      },
      {
        user: employeeUser._id,
        title: 'System Maintenance Scheduled',
        message: 'Scheduled maintenance will occur on Sunday, 2:00 AM - 4:00 AM. Some features may be temporarily unavailable.',
        type: 'warning',
        priority: 'high',
        category: 'system',
        targetRoles: ['admin', 'employee'],
        data: { maintenanceDate: '2024-02-04', startTime: '02:00', endTime: '04:00' }
      },
      {
        user: employeeUser._id,
        title: 'Customer Return Request',
        message: 'Return request submitted for order #ORD-2024-001. Reason: Product damaged during shipping.',
        type: 'warning',
        priority: 'high',
        category: 'updates',
        targetRoles: ['admin', 'employee'],
        data: { orderId: 'ORD-2024-001', reason: 'Product damaged during shipping' }
      },
      {
        user: employeeUser._id,
        title: 'Database Backup Completed',
        message: 'Daily database backup completed successfully at 3:00 AM. All data is secure.',
        type: 'success',
        priority: 'low',
        category: 'system',
        targetRoles: ['admin', 'employee'],
        data: { backupTime: '03:00', backupSize: '2.4GB', status: 'completed' }
      }
    ];

    // Create notifications with different timestamps
    for (let i = 0; i < employeeNotifications.length; i++) {
      const notification = employeeNotifications[i];
      
      // Create notifications with different timestamps (spread over last 5 days)
      const createdAt = new Date();
      createdAt.setDate(createdAt.getDate() - Math.floor(i / 2));
      createdAt.setHours(createdAt.getHours() - (i * 3));
      
      // Mark some notifications as read
      const isRead = i > 4; // First 5 notifications are unread
      
      const newNotification = new Notification({
        ...notification,
        read: isRead,
        readAt: isRead ? new Date(createdAt.getTime() + 3600000) : null, // 1 hour after creation
        createdAt
      });

      await newNotification.save();
    }

    console.log(`✅ Created ${employeeNotifications.length} employee-appropriate notifications`);
    console.log('📊 Employee notification breakdown:');
    console.log('- Unread: 5 notifications');
    console.log('- Read: 3 notifications');
    console.log('- Updates: 4 notifications');
    console.log('- Reports: 1 notification');
    console.log('- System: 3 notifications');
    console.log('- No admin-specific notifications (staff management, product creation, etc.)');

  } catch (error) {
    console.error('❌ Error creating employee notifications:', error);
  } finally {
    await mongoose.disconnect();
    console.log('🔌 Disconnected from MongoDB');
  }
}

createEmployeeNotifications();
