const axios = require('axios');

const BASE_URL = 'http://localhost:5000';

async function testLoginFlow() {
    console.log('🧪 Testing Authentication Flow...\n');

    try {
        // Test 1: Admin Login
        console.log('1. Testing Admin Login...');
        const adminLoginResponse = await axios.post(`${BASE_URL}/api/auth/login-admin`, {
            email: '<EMAIL>',
            password: 'Admin123!'
        });

        console.log('✅ Admin Login Response:');
        console.log('   Status:', adminLoginResponse.status);
        console.log('   Success:', adminLoginResponse.data.success);
        console.log('   Message:', adminLoginResponse.data.message);
        console.log('   Requires 2FA:', adminLoginResponse.data.requiresTwoFactor);
        console.log('   User ID:', adminLoginResponse.data.userId);
        console.log('   Email:', adminLoginResponse.data.email);
        console.log('   Role:', adminLoginResponse.data.role);

        if (adminLoginResponse.data.success && adminLoginResponse.data.userId) {
            console.log('\n📱 Check the backend server console for the 6-digit verification code');
            console.log('🔑 User ID for verification:', adminLoginResponse.data.userId);
        }

        console.log('\n' + '='.repeat(50) + '\n');

        // Test 2: Employee Login
        console.log('2. Testing Employee Login...');
        const employeeLoginResponse = await axios.post(`${BASE_URL}/api/auth/login-employee`, {
            email: '<EMAIL>',
            password: 'Employee123!'
        });

        console.log('✅ Employee Login Response:');
        console.log('   Status:', employeeLoginResponse.status);
        console.log('   Success:', employeeLoginResponse.data.success);
        console.log('   Message:', employeeLoginResponse.data.message);
        console.log('   Requires 2FA:', employeeLoginResponse.data.requiresTwoFactor);
        console.log('   User ID:', employeeLoginResponse.data.userId);
        console.log('   Email:', employeeLoginResponse.data.email);
        console.log('   Role:', employeeLoginResponse.data.role);

        if (employeeLoginResponse.data.success && employeeLoginResponse.data.userId) {
            console.log('\n📱 Check the backend server console for the 6-digit verification code');
            console.log('🔑 User ID for verification:', employeeLoginResponse.data.userId);
        }

        console.log('\n' + '='.repeat(50) + '\n');

        // Test 3: Invalid Credentials
        console.log('3. Testing Invalid Credentials...');
        try {
            await axios.post(`${BASE_URL}/api/auth/login-admin`, {
                email: '<EMAIL>',
                password: 'WrongPassword'
            });
        } catch (error) {
            console.log('✅ Invalid Credentials Response:');
            console.log('   Status:', error.response.status);
            console.log('   Success:', error.response.data.success);
            console.log('   Message:', error.response.data.message);
        }

        console.log('\n' + '='.repeat(50) + '\n');

        // Test 4: Non-existent User
        console.log('4. Testing Non-existent User...');
        try {
            await axios.post(`${BASE_URL}/api/auth/login-admin`, {
                email: '<EMAIL>',
                password: 'Password123!'
            });
        } catch (error) {
            console.log('✅ Non-existent User Response:');
            console.log('   Status:', error.response.status);
            console.log('   Success:', error.response.data.success);
            console.log('   Message:', error.response.data.message);
        }

        console.log('\n🎉 All tests completed successfully!');
        console.log('\n📋 Summary:');
        console.log('   - Admin login endpoint is working');
        console.log('   - Employee login endpoint is working');
        console.log('   - Error handling is working');
        console.log('   - Two-factor codes are being generated');
        console.log('\n💡 Next steps:');
        console.log('   1. Check backend console for 2FA codes');
        console.log('   2. Test the frontend login form');
        console.log('   3. Test two-factor verification');

    } catch (error) {
        console.error('❌ Test failed:', error.message);
        if (error.response) {
            console.error('   Status:', error.response.status);
            console.error('   Data:', error.response.data);
        }
    }
}

// Run the test
testLoginFlow();
