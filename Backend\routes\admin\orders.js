const express = require('express');
const router = express.Router();
const {
    getAllOrders,
    getOrderStats,
    updateOrderStatus
} = require('../../controllers/orderController');
const { protect, authorize } = require('../../middleware/auth');

// All routes are protected and require admin access
router.use(protect);
router.use(authorize('admin'));

// @route   GET /api/admin/orders
// @desc    Get all orders (admin)
// @access  Private/Admin
router.get('/', require('../../controllers/orderController').getAllOrders);

// @route   GET /api/admin/orders/:id
// @desc    Get order by ID (admin)
// @access  Private/Admin
router.get('/:id', require('../../controllers/orderController').getOrderById);

// @route   PUT /api/admin/orders/:id/status
// @desc    Update order status (admin)
// @access  Private/Admin
router.put('/:id/status', require('../../controllers/orderController').updateOrderStatus);

// @route   GET /api/admin/orders/stats
// @desc    Get order statistics (admin)
// @access  Private/Admin
router.get('/stats', require('../../controllers/orderController').getOrderStats);

// @route   DELETE /api/admin/orders/:id
// @desc    Delete order (admin)
// @access  Private/Admin
router.delete('/:id', require('../../controllers/orderController').deleteOrder);

module.exports = router;
