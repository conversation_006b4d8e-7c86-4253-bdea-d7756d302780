{"info": {"_postman_id": "drkumar-full-api-collection", "name": "Dr <PERSON> Full API Collection", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json"}, "item": [{"name": "Admin - Products", "item": [{"name": "Create Product (with images)", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer <token>", "type": "text"}], "body": {"mode": "formdata", "formdata": [{"key": "name", "value": "Sample Product", "type": "text"}, {"key": "description", "value": "Description here", "type": "text"}, {"key": "categoryId", "value": "<categoryId>", "type": "text"}, {"key": "price", "value": "100", "type": "text"}, {"key": "category", "value": "Category Name", "type": "text"}, {"key": "inStock", "value": "true", "type": "text"}, {"key": "isFeatured", "value": "false", "type": "text"}, {"key": "images", "type": "file", "src": ""}]}, "url": {"raw": "/api/admin/products", "path": ["api", "admin", "products"]}}}, {"name": "Update Product (with images)", "request": {"method": "PUT", "header": [{"key": "Authorization", "value": "Bearer <token>", "type": "text"}], "body": {"mode": "formdata", "formdata": [{"key": "name", "value": "Updated Product", "type": "text"}, {"key": "images", "type": "file", "src": ""}]}, "url": {"raw": "/api/admin/products/:id", "path": ["api", "admin", "products", ":id"]}}}, {"name": "Delete Product", "request": {"method": "DELETE", "header": [{"key": "Authorization", "value": "Bearer <token>", "type": "text"}], "url": {"raw": "/api/admin/products/:id", "path": ["api", "admin", "products", ":id"]}}}, {"name": "Get Product Stats", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer <token>", "type": "text"}], "url": {"raw": "/api/admin/products/stats", "path": ["api", "admin", "products", "stats"]}}}, {"name": "Get All Products (admin)", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer <token>", "type": "text"}], "url": {"raw": "/api/admin/products", "path": ["api", "admin", "products"]}}}, {"name": "Get Product By ID (admin)", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer <token>", "type": "text"}], "url": {"raw": "/api/admin/products/:id", "path": ["api", "admin", "products", ":id"]}}}, {"name": "Toggle Featured Status", "request": {"method": "PUT", "header": [{"key": "Authorization", "value": "Bearer <token>", "type": "text"}], "url": {"raw": "/api/admin/products/:id/featured", "path": ["api", "admin", "products", ":id", "featured"]}}}, {"name": "Toggle Stock Status", "request": {"method": "PUT", "header": [{"key": "Authorization", "value": "Bearer <token>", "type": "text"}], "url": {"raw": "/api/admin/products/:id/stock", "path": ["api", "admin", "products", ":id", "stock"]}}}]}, {"name": "Admin - Banners", "item": [{"name": "Create Banner (with image)", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer <token>", "type": "text"}], "body": {"mode": "formdata", "formdata": [{"key": "title", "value": "Banner Title", "type": "text"}, {"key": "description", "value": "Banner Description", "type": "text"}, {"key": "image", "type": "file", "src": ""}, {"key": "link", "value": "", "type": "text"}, {"key": "priority", "value": "0", "type": "text"}, {"key": "startDate", "value": "", "type": "text"}, {"key": "endDate", "value": "", "type": "text"}, {"key": "isActive", "value": "true", "type": "text"}]}, "url": {"raw": "/api/admin/banners", "path": ["api", "admin", "banners"]}}}, {"name": "Update Banner (with image)", "request": {"method": "PUT", "header": [{"key": "Authorization", "value": "Bearer <token>", "type": "text"}], "body": {"mode": "formdata", "formdata": [{"key": "title", "value": "Updated Banner", "type": "text"}, {"key": "image", "type": "file", "src": ""}]}, "url": {"raw": "/api/admin/banners/:id", "path": ["api", "admin", "banners", ":id"]}}}, {"name": "Delete Banner", "request": {"method": "DELETE", "header": [{"key": "Authorization", "value": "Bearer <token>", "type": "text"}], "url": {"raw": "/api/admin/banners/:id", "path": ["api", "admin", "banners", ":id"]}}}, {"name": "Toggle Banner Status", "request": {"method": "PUT", "header": [{"key": "Authorization", "value": "Bearer <token>", "type": "text"}], "url": {"raw": "/api/admin/banners/:id/toggle-active", "path": ["api", "admin", "banners", ":id", "toggle-active"]}}}, {"name": "Update Banner Priority", "request": {"method": "PUT", "header": [{"key": "Authorization", "value": "Bearer <token>", "type": "text"}], "url": {"raw": "/api/admin/banners/:id/priority", "path": ["api", "admin", "banners", ":id", "priority"]}}}, {"name": "Get Banner Stats", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer <token>", "type": "text"}], "url": {"raw": "/api/admin/banners/stats", "path": ["api", "admin", "banners", "stats"]}}}, {"name": "Get Expiring Soon Banners", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer <token>", "type": "text"}], "url": {"raw": "/api/admin/banners/expiring-soon", "path": ["api", "admin", "banners", "expiring-soon"]}}}, {"name": "Bulk Update Banners", "request": {"method": "PUT", "header": [{"key": "Authorization", "value": "Bearer <token>", "type": "text"}], "url": {"raw": "/api/admin/banners/bulk-update", "path": ["api", "admin", "banners", "bulk-update"]}}}, {"name": "Get All Banners (admin)", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer <token>", "type": "text"}], "url": {"raw": "/api/admin/banners", "path": ["api", "admin", "banners"]}}}, {"name": "Get Active Banners (admin)", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer <token>", "type": "text"}], "url": {"raw": "/api/admin/banners/active", "path": ["api", "admin", "banners", "active"]}}}, {"name": "Get Banner By ID (admin)", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer <token>", "type": "text"}], "url": {"raw": "/api/admin/banners/:id", "path": ["api", "admin", "banners", ":id"]}}}]}, {"name": "Admin - Categories", "item": [{"name": "Create Category", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer <token>", "type": "text"}], "body": {"mode": "formdata", "formdata": [{"key": "name", "value": "New Category", "type": "text"}, {"key": "description", "value": "Category Description", "type": "text"}]}, "url": {"raw": "/api/admin/categories", "path": ["api", "admin", "categories"]}}}, {"name": "Update Category", "request": {"method": "PUT", "header": [{"key": "Authorization", "value": "Bearer <token>", "type": "text"}], "body": {"mode": "formdata", "formdata": [{"key": "name", "value": "Updated Category", "type": "text"}, {"key": "description", "value": "Updated Description", "type": "text"}]}, "url": {"raw": "/api/admin/categories/:id", "path": ["api", "admin", "categories", ":id"]}}}, {"name": "Delete Category", "request": {"method": "DELETE", "header": [{"key": "Authorization", "value": "Bearer <token>", "type": "text"}], "url": {"raw": "/api/admin/categories/:id", "path": ["api", "admin", "categories", ":id"]}}}, {"name": "Get All Categories (admin)", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer <token>", "type": "text"}], "url": {"raw": "/api/admin/categories", "path": ["api", "admin", "categories"]}}}, {"name": "Get Category By ID (admin)", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer <token>", "type": "text"}], "url": {"raw": "/api/admin/categories/:id", "path": ["api", "admin", "categories", ":id"]}}}]}, {"name": "Admin - Users", "item": [{"name": "Get All Users (admin)", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer <token>", "type": "text"}], "url": {"raw": "/api/admin/users", "path": ["api", "admin", "users"]}}}, {"name": "Get User By ID (admin)", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer <token>", "type": "text"}], "url": {"raw": "/api/admin/users/:id", "path": ["api", "admin", "users", ":id"]}}}, {"name": "Update User Role", "request": {"method": "PUT", "header": [{"key": "Authorization", "value": "Bearer <token>", "type": "text"}], "body": {"mode": "formdata", "formdata": [{"key": "role", "value": "admin", "type": "text"}]}, "url": {"raw": "/api/admin/users/:id/role", "path": ["api", "admin", "users", ":id", "role"]}}}, {"name": "Delete User", "request": {"method": "DELETE", "header": [{"key": "Authorization", "value": "Bearer <token>", "type": "text"}], "url": {"raw": "/api/admin/users/:id", "path": ["api", "admin", "users", ":id"]}}}]}, {"name": "Admin - Orders", "item": [{"name": "Get All Orders (admin)", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer <token>", "type": "text"}], "url": {"raw": "/api/admin/orders", "path": ["api", "admin", "orders"]}}}, {"name": "Get Order By ID (admin)", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer <token>", "type": "text"}], "url": {"raw": "/api/admin/orders/:id", "path": ["api", "admin", "orders", ":id"]}}}, {"name": "Update Order Status", "request": {"method": "PUT", "header": [{"key": "Authorization", "value": "Bearer <token>", "type": "text"}], "body": {"mode": "formdata", "formdata": [{"key": "status", "value": "delivered", "type": "text"}]}, "url": {"raw": "/api/admin/orders/:id/status", "path": ["api", "admin", "orders", ":id", "status"]}}}, {"name": "Delete Order", "request": {"method": "DELETE", "header": [{"key": "Authorization", "value": "Bearer <token>", "type": "text"}], "url": {"raw": "/api/admin/orders/:id", "path": ["api", "admin", "orders", ":id"]}}}]}, {"name": "Admin - Reviews", "item": [{"name": "Get All Reviews (admin)", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer <token>", "type": "text"}], "url": {"raw": "/api/admin/reviews", "path": ["api", "admin", "reviews"]}}}, {"name": "Get Review By ID (admin)", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer <token>", "type": "text"}], "url": {"raw": "/api/admin/reviews/:id", "path": ["api", "admin", "reviews", ":id"]}}}, {"name": "Delete Review", "request": {"method": "DELETE", "header": [{"key": "Authorization", "value": "Bearer <token>", "type": "text"}], "url": {"raw": "/api/admin/reviews/:id", "path": ["api", "admin", "reviews", ":id"]}}}]}, {"name": "Admin - Settings", "item": [{"name": "Update Site Settings", "request": {"method": "PUT", "header": [{"key": "Authorization", "value": "Bearer <token>", "type": "text"}], "body": {"mode": "formdata", "formdata": [{"key": "siteName", "value": "My Site", "type": "text"}, {"key": "siteDescription", "value": "Description of my site", "type": "text"}, {"key": "siteLogo", "type": "file", "src": ""}, {"key": "siteFavicon", "type": "file", "src": ""}]}, "url": {"raw": "/api/admin/settings", "path": ["api", "admin", "settings"]}}}, {"name": "Get Site Settings", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer <token>", "type": "text"}], "url": {"raw": "/api/admin/settings", "path": ["api", "admin", "settings"]}}}]}, {"name": "Admin - Analytics", "item": [{"name": "Get Overall Stats", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer <token>", "type": "text"}], "url": {"raw": "/api/admin/analytics/overall-stats", "path": ["api", "admin", "analytics", "overall-stats"]}}}, {"name": "Get Monthly Stats", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer <token>", "type": "text"}], "url": {"raw": "/api/admin/analytics/monthly-stats", "path": ["api", "admin", "analytics", "monthly-stats"]}}}, {"name": "Get Daily Stats", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer <token>", "type": "text"}], "url": {"raw": "/api/admin/analytics/daily-stats", "path": ["api", "admin", "analytics", "daily-stats"]}}}]}, {"name": "Admin - Files", "item": [{"name": "Upload File", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer <token>", "type": "text"}], "body": {"mode": "formdata", "formdata": [{"key": "file", "type": "file", "src": ""}]}, "url": {"raw": "/api/admin/files/upload", "path": ["api", "admin", "files", "upload"]}}}, {"name": "Get File", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer <token>", "type": "text"}], "url": {"raw": "/api/admin/files/:id", "path": ["api", "admin", "files", ":id"]}}}, {"name": "Delete File", "request": {"method": "DELETE", "header": [{"key": "Authorization", "value": "Bearer <token>", "type": "text"}], "url": {"raw": "/api/admin/files/:id", "path": ["api", "admin", "files", ":id"]}}}]}, {"name": "Admin - Logs", "item": [{"name": "Get All Logs (admin)", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer <token>", "type": "text"}], "url": {"raw": "/api/admin/logs", "path": ["api", "admin", "logs"]}}}, {"name": "Get Log By ID (admin)", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer <token>", "type": "text"}], "url": {"raw": "/api/admin/logs/:id", "path": ["api", "admin", "logs", ":id"]}}}, {"name": "Delete Log", "request": {"method": "DELETE", "header": [{"key": "Authorization", "value": "Bearer <token>", "type": "text"}], "url": {"raw": "/api/admin/logs/:id", "path": ["api", "admin", "logs", ":id"]}}}]}, {"name": "User - Users", "item": [{"name": "Register User", "request": {"method": "POST", "body": {"mode": "formdata", "formdata": [{"key": "name", "value": "Test User", "type": "text"}, {"key": "email", "value": "<EMAIL>", "type": "text"}, {"key": "password", "value": "Password123!", "type": "text"}, {"key": "phone", "value": "1234567890", "type": "text"}, {"key": "department", "value": "", "type": "text"}, {"key": "role", "value": "user", "type": "text"}, {"key": "shippingAddress", "value": "", "type": "text"}, {"key": "paymentMethod", "value": "", "type": "text"}]}, "url": {"raw": "/api/users/register", "path": ["api", "users", "register"]}}}, {"name": "Login User", "request": {"method": "POST", "body": {"mode": "formdata", "formdata": [{"key": "email", "value": "<EMAIL>", "type": "text"}, {"key": "password", "value": "Password123!", "type": "text"}]}, "url": {"raw": "/api/users/login", "path": ["api", "users", "login"]}}}, {"name": "<PERSON><PERSON><PERSON>", "request": {"method": "GET", "url": {"raw": "/api/users/verify-email/:token", "path": ["api", "users", "verify-email", ":token"]}}}, {"name": "Forgot Password", "request": {"method": "POST", "body": {"mode": "formdata", "formdata": [{"key": "email", "value": "<EMAIL>", "type": "text"}]}, "url": {"raw": "/api/users/forgot-password", "path": ["api", "users", "forgot-password"]}}}, {"name": "Reset Password", "request": {"method": "POST", "body": {"mode": "formdata", "formdata": [{"key": "password", "value": "NewPassword123!", "type": "text"}]}, "url": {"raw": "/api/users/reset-password/:token", "path": ["api", "users", "reset-password", ":token"]}}}, {"name": "Get User Profile", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer <token>", "type": "text"}], "url": {"raw": "/api/user/users/profile", "path": ["api", "user", "users", "profile"]}}}, {"name": "Update User Profile (with image)", "request": {"method": "PUT", "header": [{"key": "Authorization", "value": "Bearer <token>", "type": "text"}], "body": {"mode": "formdata", "formdata": [{"key": "name", "value": "Updated Name", "type": "text"}, {"key": "profileImage", "type": "file", "src": ""}]}, "url": {"raw": "/api/user/users/profile", "path": ["api", "user", "users", "profile"]}}}, {"name": "Change Password", "request": {"method": "PUT", "header": [{"key": "Authorization", "value": "Bearer <token>", "type": "text"}], "body": {"mode": "formdata", "formdata": [{"key": "currentPassword", "value": "Password123!", "type": "text"}, {"key": "newPassword", "value": "NewPassword123!", "type": "text"}]}, "url": {"raw": "/api/user/users/change-password", "path": ["api", "user", "users", "change-password"]}}}]}, {"name": "User - <PERSON><PERSON>", "item": [{"name": "Get Cart", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer <token>", "type": "text"}], "url": {"raw": "/api/user/cart", "path": ["api", "user", "cart"]}}}, {"name": "Add to Cart", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer <token>", "type": "text"}], "body": {"mode": "formdata", "formdata": [{"key": "productId", "value": "<productId>", "type": "text"}, {"key": "quantity", "value": "1", "type": "text"}]}, "url": {"raw": "/api/user/cart/add", "path": ["api", "user", "cart", "add"]}}}, {"name": "Update Cart Item", "request": {"method": "PUT", "header": [{"key": "Authorization", "value": "Bearer <token>", "type": "text"}], "body": {"mode": "formdata", "formdata": [{"key": "quantity", "value": "2", "type": "text"}]}, "url": {"raw": "/api/user/cart/update/:itemId", "path": ["api", "user", "cart", "update", ":itemId"]}}}, {"name": "Remove from Cart", "request": {"method": "DELETE", "header": [{"key": "Authorization", "value": "Bearer <token>", "type": "text"}], "url": {"raw": "/api/user/cart/remove/:itemId", "path": ["api", "user", "cart", "remove", ":itemId"]}}}, {"name": "Clear Cart", "request": {"method": "DELETE", "header": [{"key": "Authorization", "value": "Bearer <token>", "type": "text"}], "url": {"raw": "/api/user/cart/clear", "path": ["api", "user", "cart", "clear"]}}}, {"name": "Get Cart Summary", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer <token>", "type": "text"}], "url": {"raw": "/api/user/cart/summary", "path": ["api", "user", "cart", "summary"]}}}, {"name": "Validate <PERSON>", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer <token>", "type": "text"}], "url": {"raw": "/api/user/cart/validate", "path": ["api", "user", "cart", "validate"]}}}, {"name": "<PERSON><PERSON>", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer <token>", "type": "text"}], "body": {"mode": "formdata", "formdata": [{"key": "cartId", "value": "<cartId>", "type": "text"}]}, "url": {"raw": "/api/user/cart/merge", "path": ["api", "user", "cart", "merge"]}}}, {"name": "Get Cart Count", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer <token>", "type": "text"}], "url": {"raw": "/api/user/cart/count", "path": ["api", "user", "cart", "count"]}}}]}, {"name": "User - Orders", "item": [{"name": "Get User Orders", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer <token>", "type": "text"}], "url": {"raw": "/api/user/orders", "path": ["api", "user", "orders"]}}}, {"name": "Get Order By ID", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer <token>", "type": "text"}], "url": {"raw": "/api/user/orders/:id", "path": ["api", "user", "orders", ":id"]}}}, {"name": "Create Order", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer <token>", "type": "text"}], "body": {"mode": "formdata", "formdata": [{"key": "items", "value": "[{\"product\":\"<productId>\",\"quantity\":1}]", "type": "text"}, {"key": "shippingAddress", "value": "{\"addressLine1\":\"123 St\",\"city\":\"City\",\"postalCode\":\"12345\",\"country\":\"Country\"}", "type": "text"}]}, "url": {"raw": "/api/user/orders", "path": ["api", "user", "orders"]}}}, {"name": "Cancel Order", "request": {"method": "PUT", "header": [{"key": "Authorization", "value": "Bearer <token>", "type": "text"}], "url": {"raw": "/api/user/orders/:id/cancel", "path": ["api", "user", "orders", ":id", "cancel"]}}}, {"name": "Update Shipping Address", "request": {"method": "PUT", "header": [{"key": "Authorization", "value": "Bearer <token>", "type": "text"}], "body": {"mode": "formdata", "formdata": [{"key": "shippingAddress", "value": "{\"addressLine1\":\"New Address\"}", "type": "text"}]}, "url": {"raw": "/api/user/orders/:id/shipping-address", "path": ["api", "user", "orders", ":id", "shipping-address"]}}}]}, {"name": "User - Products", "item": [{"name": "Get All Products", "request": {"method": "GET", "url": {"raw": "/api/products", "path": ["api", "products"]}}}, {"name": "Get Featured Products", "request": {"method": "GET", "url": {"raw": "/api/products/featured", "path": ["api", "products", "featured"]}}}, {"name": "Get Product By ID", "request": {"method": "GET", "url": {"raw": "/api/products/:id", "path": ["api", "products", ":id"]}}}, {"name": "Add Product Review", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer <token>", "type": "text"}], "body": {"mode": "formdata", "formdata": [{"key": "rating", "value": "5", "type": "text"}, {"key": "comment", "value": "Great product!", "type": "text"}]}, "url": {"raw": "/api/products/:id/reviews", "path": ["api", "products", ":id", "reviews"]}}}, {"name": "Update Product Review", "request": {"method": "PUT", "header": [{"key": "Authorization", "value": "Bearer <token>", "type": "text"}], "body": {"mode": "formdata", "formdata": [{"key": "rating", "value": "4", "type": "text"}, {"key": "comment", "value": "Updated review", "type": "text"}]}, "url": {"raw": "/api/products/:id/reviews/:reviewId", "path": ["api", "products", ":id", "reviews", ":reviewId"]}}}, {"name": "Delete Product Review", "request": {"method": "DELETE", "header": [{"key": "Authorization", "value": "Bearer <token>", "type": "text"}], "url": {"raw": "/api/products/:id/reviews/:reviewId", "path": ["api", "products", ":id", "reviews", ":reviewId"]}}}, {"name": "Get Product Stats (user)", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer <token>", "type": "text"}], "url": {"raw": "/api/user/products/stats", "path": ["api", "user", "products", "stats"]}}}]}]}