const axios = require('axios');

const BASE_URL = 'http://localhost:5000';

async function testOrdersAPI() {
    console.log('🧪 Testing Orders API...\n');

    try {
        // First, login to get a token
        console.log('1. Logging in to get auth token...');
        const loginResponse = await axios.post(`${BASE_URL}/api/auth/login-admin`, {
            email: '<EMAIL>',
            password: 'Admin123!'
        });

        if (!loginResponse.data.success) {
            throw new Error('Login failed');
        }

        console.log('✅ Login successful');

        // Get 2FA code (for testing, we'll skip this and use a mock token)
        // In real scenario, you'd complete the 2FA flow

        // Test getting orders without auth first
        console.log('\n2. Testing orders endpoint without auth...');
        try {
            await axios.get(`${BASE_URL}/api/admin/orders`);
            console.log('❌ Orders endpoint should require authentication');
        } catch (error) {
            console.log('✅ Orders endpoint properly requires authentication');
            console.log('   Status:', error.response?.status);
            console.log('   Message:', error.response?.data?.message);
        }

        // Test with mock token (for testing purposes)
        console.log('\n3. Testing orders endpoint structure...');
        try {
            const ordersResponse = await axios.get(`${BASE_URL}/api/admin/orders`, {
                headers: {
                    'Authorization': 'Bearer mock-token-for-testing'
                }
            });
            console.log('✅ Orders endpoint response:', ordersResponse.data);
        } catch (error) {
            console.log('📋 Orders endpoint error (expected due to auth):');
            console.log('   Status:', error.response?.status);
            console.log('   Message:', error.response?.data?.message);
        }

        // Test order stats endpoint
        console.log('\n4. Testing order stats endpoint...');
        try {
            const statsResponse = await axios.get(`${BASE_URL}/api/admin/orders/stats`, {
                headers: {
                    'Authorization': 'Bearer mock-token-for-testing'
                }
            });
            console.log('✅ Order stats response:', statsResponse.data);
        } catch (error) {
            console.log('📊 Order stats error (expected due to auth):');
            console.log('   Status:', error.response?.status);
            console.log('   Message:', error.response?.data?.message);
        }

        console.log('\n💡 API endpoints are properly configured and protected');

    } catch (error) {
        console.error('❌ Test failed:', error.message);
        if (error.response) {
            console.error('   Status:', error.response.status);
            console.error('   Data:', error.response.data);
        }
    }
}

// Run the test
testOrdersAPI();
