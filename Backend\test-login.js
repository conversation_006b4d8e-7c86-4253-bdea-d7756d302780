const http = require("http");

function makeRequest(options, data) {
  return new Promise((resolve, reject) => {
    const req = http.request(options, (res) => {
      let body = "";
      res.on("data", (chunk) => {
        body += chunk;
      });
      res.on("end", () => {
        try {
          const jsonBody = JSON.parse(body);
          resolve({ status: res.statusCode, data: jsonBody });
        } catch (e) {
          resolve({ status: res.statusCode, data: body });
        }
      });
    });

    req.on("error", (err) => {
      reject(err);
    });

    if (data) {
      req.write(JSON.stringify(data));
    }
    req.end();
  });
}

async function testLogin() {
  console.log("🧪 Testing Admin Login Flow...\n");

  try {
    // Test Admin Login
    console.log("1. Testing Admin Login...");
    const loginOptions = {
      hostname: "localhost",
      port: 5000,
      path: "/api/auth/login-admin",
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
    };

    const loginData = {
      email: "<EMAIL>",
      password: "Admin123!",
    };

    const loginResponse = await makeRequest(loginOptions, loginData);
    console.log("Status:", loginResponse.status);
    console.log("Response:", loginResponse.data);

    if (loginResponse.data.success && loginResponse.data.userId) {
      console.log("\n✅ Admin login successful!");
      console.log("📱 Check server logs for the 6-digit verification code");
      console.log("🔑 User ID for verification:", loginResponse.data.userId);

      // Test with a demo code (you'll need to replace with actual code from logs)
      console.log("\n2. Testing Two-Factor Verification with demo code...");
      const verifyOptions = {
        hostname: "localhost",
        port: 5000,
        path: "/api/auth/verify-code",
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
      };

      const verifyData = {
        userId: loginResponse.data.userId,
        code: "601479", // Actual code from database
      };

      const verifyResponse = await makeRequest(verifyOptions, verifyData);
      console.log("Verify Status:", verifyResponse.status);
      console.log("Verify Response:", verifyResponse.data);

      if (verifyResponse.data.success) {
        console.log("\n✅ Two-factor verification successful!");
        console.log("🎟️  JWT Token:", verifyResponse.data.token);
      } else {
        console.log(
          "\n⚠️  Two-factor verification failed. Use the actual code from server logs."
        );
      }
    }
  } catch (error) {
    console.error("❌ Error:", error.message);
  }
}

testLogin();
