const express = require("express");
const dotenv = require("dotenv");
dotenv.config();
const cors = require("cors");

const app = express();
const port = process.env.PORT || 5000;

// Middleware
app.use(express.json({ limit: "10mb" }));
app.use(express.urlencoded({ extended: true, limit: "10mb" }));

// CORS configuration
app.use(
  cors({
    origin: process.env.CLIENT_URL || "http://localhost:5173",
    credentials: true,
  })
);

// Health check endpoint
app.get("/api/health", (req, res) => {
  res.json({
    success: true,
    message: "Dr. <PERSON>end is running",
    timestamp: new Date().toISOString(),
    environment: process.env.NODE_ENV || "development",
  });
});

// Simple test route
app.get("/api/test", (req, res) => {
  res.json({
    success: true,
    message: "Test route working",
    data: {
      products: [
        {
          _id: "1",
          name: "Test Product",
          price: 100,
          inStock: true,
          category: "Test Category",
        },
      ],
    },
  });
});

// Mock products data
const mockProducts = [
  {
    _id: "1",
    name: "Power Stride Juice",
    categoryId: "cat1",
    category: "Health Drinks",
    description: "Pure Aloe Vera juice for digestive health",
    price: 249,
    inStock: true,
    isFeatured: true,
    images: [{ url: "/api/placeholder/60/60", altText: "Power Stride Juice" }],
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString(),
  },
  {
    _id: "2",
    name: "Ashwagandha Capsules",
    categoryId: "cat2",
    category: "Supplements",
    description: "Premium Ashwagandha for stress relief",
    price: 599,
    inStock: true,
    isFeatured: false,
    images: [
      { url: "/api/placeholder/60/60", altText: "Ashwagandha Capsules" },
    ],
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString(),
  },
  {
    _id: "3",
    name: "Triphala Churna",
    categoryId: "cat3",
    category: "Ayurvedic",
    description: "Traditional Triphala powder",
    price: 149,
    inStock: false,
    isFeatured: false,
    images: [{ url: "/api/placeholder/60/60", altText: "Triphala Churna" }],
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString(),
  },
];

// Mock categories data
const mockCategories = [
  { _id: "cat1", name: "Health Drinks", active: true },
  { _id: "cat2", name: "Supplements", active: true },
  { _id: "cat3", name: "Ayurvedic", active: true },
];

// Products API endpoints
app.get("/api/admin/products", (req, res) => {
  const { page = 1, limit = 10, search, inStock, category } = req.query;

  let filteredProducts = [...mockProducts];

  // Apply filters
  if (search) {
    filteredProducts = filteredProducts.filter(
      (p) =>
        p.name.toLowerCase().includes(search.toLowerCase()) ||
        p.description.toLowerCase().includes(search.toLowerCase())
    );
  }

  if (inStock !== undefined) {
    filteredProducts = filteredProducts.filter(
      (p) => p.inStock === (inStock === "true")
    );
  }

  if (category && category !== "all") {
    filteredProducts = filteredProducts.filter((p) => p.category === category);
  }

  // Pagination
  const startIndex = (page - 1) * limit;
  const endIndex = startIndex + parseInt(limit);
  const paginatedProducts = filteredProducts.slice(startIndex, endIndex);

  res.json({
    success: true,
    data: {
      products: paginatedProducts,
      pagination: {
        page: parseInt(page),
        limit: parseInt(limit),
        total: filteredProducts.length,
        pages: Math.ceil(filteredProducts.length / limit),
      },
    },
  });
});

// Get product by ID
app.get("/api/admin/products/:id", (req, res) => {
  const product = mockProducts.find((p) => p._id === req.params.id);
  if (!product) {
    return res.status(404).json({
      success: false,
      message: "Product not found",
    });
  }

  res.json({
    success: true,
    data: { product },
  });
});

// Create product
app.post("/api/admin/products", (req, res) => {
  const newProduct = {
    _id: Date.now().toString(),
    ...req.body,
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString(),
  };

  mockProducts.push(newProduct);

  res.status(201).json({
    success: true,
    message: "Product created successfully",
    data: { product: newProduct },
  });
});

// Update product
app.put("/api/admin/products/:id", (req, res) => {
  const productIndex = mockProducts.findIndex((p) => p._id === req.params.id);
  if (productIndex === -1) {
    return res.status(404).json({
      success: false,
      message: "Product not found",
    });
  }

  mockProducts[productIndex] = {
    ...mockProducts[productIndex],
    ...req.body,
    updatedAt: new Date().toISOString(),
  };

  res.json({
    success: true,
    message: "Product updated successfully",
    data: { product: mockProducts[productIndex] },
  });
});

// Delete product
app.delete("/api/admin/products/:id", (req, res) => {
  const productIndex = mockProducts.findIndex((p) => p._id === req.params.id);
  if (productIndex === -1) {
    return res.status(404).json({
      success: false,
      message: "Product not found",
    });
  }

  mockProducts.splice(productIndex, 1);

  res.json({
    success: true,
    message: "Product deleted successfully",
  });
});

// Toggle stock status
app.put("/api/admin/products/:id/stock", (req, res) => {
  const productIndex = mockProducts.findIndex((p) => p._id === req.params.id);
  if (productIndex === -1) {
    return res.status(404).json({
      success: false,
      message: "Product not found",
    });
  }

  mockProducts[productIndex].inStock = !mockProducts[productIndex].inStock;
  mockProducts[productIndex].updatedAt = new Date().toISOString();

  res.json({
    success: true,
    message: "Stock status updated successfully",
    data: { product: mockProducts[productIndex] },
  });
});

// Get product stats
app.get("/api/admin/products/stats", (req, res) => {
  const stats = {
    totalProducts: mockProducts.length,
    activeProducts: mockProducts.filter((p) => p.inStock).length,
    inactiveProducts: mockProducts.filter((p) => !p.inStock).length,
    averagePrice:
      mockProducts.reduce((sum, p) => sum + p.price, 0) / mockProducts.length,
  };

  res.json({
    success: true,
    data: { stats },
  });
});

// Categories API endpoints
app.get("/api/admin/categories", (req, res) => {
  res.json({
    success: true,
    data: { categories: mockCategories },
  });
});

// Mock orders data
const mockOrders = [
  {
    _id: "order1",
    orderNumber: "ORD-2024-001",
    customerId: "cust1",
    customerName: "John Doe",
    customerEmail: "<EMAIL>",
    customerPhone: "+91-9876543210",
    items: [
      {
        productId: "1",
        productName: "Power Stride Juice",
        quantity: 2,
        price: 249,
        total: 498,
      },
    ],
    subtotal: 498,
    tax: 50,
    shipping: 50,
    total: 598,
    status: "confirmed",
    paymentStatus: "paid",
    paymentMethod: "Credit Card",
    shippingAddress: {
      street: "123 Main St",
      city: "Mumbai",
      state: "Maharashtra",
      zipCode: "400001",
      country: "India",
    },
    notes: "Please deliver between 10 AM - 6 PM",
    trackingNumber: "TRK123456789",
    estimatedDelivery: "2024-01-25",
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString(),
  },
  {
    _id: "order2",
    orderNumber: "ORD-2024-002",
    customerId: "cust2",
    customerName: "Jane Smith",
    customerEmail: "<EMAIL>",
    customerPhone: "+91-9876543211",
    items: [
      {
        productId: "2",
        productName: "Ashwagandha Capsules",
        quantity: 1,
        price: 599,
        total: 599,
      },
    ],
    subtotal: 599,
    tax: 60,
    shipping: 0,
    total: 659,
    status: "processing",
    paymentStatus: "paid",
    paymentMethod: "UPI",
    shippingAddress: {
      street: "456 Oak Ave",
      city: "Delhi",
      state: "Delhi",
      zipCode: "110001",
      country: "India",
    },
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString(),
  },
  {
    _id: "order3",
    orderNumber: "ORD-2024-003",
    customerId: "cust3",
    customerName: "Bob Johnson",
    customerEmail: "<EMAIL>",
    customerPhone: "+91-9876543212",
    items: [
      {
        productId: "3",
        productName: "Triphala Churna",
        quantity: 3,
        price: 149,
        total: 447,
      },
    ],
    subtotal: 447,
    tax: 45,
    shipping: 50,
    total: 542,
    status: "pending",
    paymentStatus: "pending",
    paymentMethod: "COD",
    shippingAddress: {
      street: "789 Pine Rd",
      city: "Bangalore",
      state: "Karnataka",
      zipCode: "560001",
      country: "India",
    },
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString(),
  },
];

// Orders API endpoints
app.get("/api/admin/orders", (req, res) => {
  const { page = 1, limit = 10, status, paymentStatus, search } = req.query;

  let filteredOrders = [...mockOrders];

  // Apply filters
  if (status && status !== "all") {
    filteredOrders = filteredOrders.filter((o) => o.status === status);
  }

  if (paymentStatus && paymentStatus !== "all") {
    filteredOrders = filteredOrders.filter(
      (o) => o.paymentStatus === paymentStatus
    );
  }

  if (search) {
    filteredOrders = filteredOrders.filter(
      (o) =>
        o.orderNumber.toLowerCase().includes(search.toLowerCase()) ||
        o.customerName.toLowerCase().includes(search.toLowerCase()) ||
        o.customerEmail?.toLowerCase().includes(search.toLowerCase())
    );
  }

  // Pagination
  const startIndex = (page - 1) * limit;
  const endIndex = startIndex + parseInt(limit);
  const paginatedOrders = filteredOrders.slice(startIndex, endIndex);

  res.json({
    success: true,
    data: {
      orders: paginatedOrders,
      pagination: {
        page: parseInt(page),
        limit: parseInt(limit),
        total: filteredOrders.length,
        pages: Math.ceil(filteredOrders.length / limit),
      },
    },
  });
});

// Get order by ID
app.get("/api/admin/orders/:id", (req, res) => {
  const order = mockOrders.find((o) => o._id === req.params.id);
  if (!order) {
    return res.status(404).json({
      success: false,
      message: "Order not found",
    });
  }

  res.json({
    success: true,
    data: { order },
  });
});

// Update order status
app.put("/api/admin/orders/:id/status", (req, res) => {
  const orderIndex = mockOrders.findIndex((o) => o._id === req.params.id);
  if (orderIndex === -1) {
    return res.status(404).json({
      success: false,
      message: "Order not found",
    });
  }

  mockOrders[orderIndex].status = req.body.status;
  mockOrders[orderIndex].updatedAt = new Date().toISOString();

  res.json({
    success: true,
    message: "Order status updated successfully",
    data: { order: mockOrders[orderIndex] },
  });
});

// Get order stats
app.get("/api/admin/orders/stats", (req, res) => {
  const stats = {
    totalOrders: mockOrders.length,
    pendingOrders: mockOrders.filter((o) => o.status === "pending").length,
    confirmedOrders: mockOrders.filter((o) => o.status === "confirmed").length,
    processingOrders: mockOrders.filter((o) => o.status === "processing")
      .length,
    shippedOrders: mockOrders.filter((o) => o.status === "shipped").length,
    deliveredOrders: mockOrders.filter((o) => o.status === "delivered").length,
    cancelledOrders: mockOrders.filter((o) => o.status === "cancelled").length,
    totalRevenue: mockOrders.reduce((sum, o) => sum + o.total, 0),
    averageOrderValue:
      mockOrders.reduce((sum, o) => sum + o.total, 0) / mockOrders.length,
  };

  res.json({
    success: true,
    data: { stats },
  });
});

// Start server
app.listen(port, () => {
  console.log("🚀 Test Server Started");
  console.log(`📍 Server running on: http://localhost:${port}`);
  console.log(`📊 Health check: http://localhost:${port}/api/health`);
  console.log(`🧪 Test route: http://localhost:${port}/api/test`);
});
