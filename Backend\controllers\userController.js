const User = require("../models/User");
const bcrypt = require("bcryptjs");
const jwt = require("jsonwebtoken");
const crypto = require("crypto");
const nodemailer = require("nodemailer");
const {
  sendEmail,
  sendTwoFactorEmail,
  sendWelcomeEmail,
  sendPasswordResetEmail,
} = require("../utils/sendEmail");
const { generateToken } = require("../middleware/authMiddleware");

// Helper to generate 6-digit verification code
const generateTwoFactorCode = () => {
  return Math.floor(100000 + Math.random() * 900000).toString();
};

// Helper to send two-factor code via email
const sendTwoFactorCode = async (user, code) => {
  try {
    // Log the code to console for development/debugging
    console.log(`🔐 Two-Factor Code for ${user.email}: ${code}`);
    console.log(`📱 Code expires in 10 minutes`);

    // Check email configuration
    if (!process.env.EMAIL_USER || !process.env.EMAIL_PASS) {
      console.error(
        "❌ Email configuration missing: EMAIL_USER or EMAIL_PASS not set"
      );
      console.log(
        "📧 For development, the code is logged above. Use it for testing."
      );
      return true; // Return true for development to allow login flow to continue
    }

    // Send branded email with two-factor code
    const emailSent = await sendTwoFactorEmail(user.email, code, user.name);

    if (!emailSent) {
      console.error("❌ Failed to send two-factor email");
      return false; // Return false to indicate email sending failure
    }

    console.log("✅ Two-factor email sent successfully");
    return true;
  } catch (error) {
    console.error("❌ Error sending two-factor code:", error);
    return false; // Return false to indicate email sending failure
  }
};

// @desc    Register a new user
// @route   POST /api/users/register
// @access  Public
const registerUser = async (req, res) => {
  try {
    const {
      name,
      email,
      password,
      phone,
      department,
      role,
      shippingAddress,
      paymentMethod,
    } = req.body;

    // Check if user already exists
    const existingUser = await User.findOne({ $or: [{ email }, { phone }] });
    if (existingUser) {
      // If user exists but password is not set (invited staff), allow password setup
      if (!existingUser.isPasswordSet && existingUser.role !== "user") {
        // This is an invited staff member, redirect to staff password setup
        return res.status(400).json({
          success: false,
          message:
            "You have been invited as staff. Please use the staff password setup process.",
          redirectToStaffSetup: true,
          email: email,
        });
      }

      // Regular user already exists
      return res.status(400).json({
        success: false,
        message: "User already exists with this email or phone",
      });
    }

    // Check password strength
    // At least 8 chars, 1 uppercase, 1 lowercase, 1 number, 1 special char
    const passwordRegex = /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[\W_]).{8,}$/;
    if (!passwordRegex.test(password)) {
      return res.status(400).json({
        success: false,
        message:
          "Password must be at least 8 characters long and include uppercase, lowercase, number, and special character.",
      });
    }
    // Hash password
    const salt = await bcrypt.genSalt(10);
    const hashedPassword = await bcrypt.hash(password, salt);

    // Generate email verification token
    const emailVerificationToken = crypto.randomBytes(32).toString("hex");

    // Create user
    const user = new User({
      name,
      email,
      password: hashedPassword,
      phone,
      department,
      role: role || "user",
      shippingAddress,
      paymentMethod,
      emailVerificationToken,
      emailVerificationExpires: Date.now() + 24 * 60 * 60 * 1000, // 24 hours
      joinedDate: Date.now(),
    });

    await user.save();

    // Send verification email
    const verificationUrl = `${process.env.CLIENT_URL}/verify-email/${emailVerificationToken}`;
    await sendEmail(
      email,
      "Email Verification",
      `Please verify your email by clicking: ${verificationUrl}`
    );

    // Return token and user info
    res.status(201).json({
      success: true,
      message: "User registered successfully. Please verify your email.",
      token: generateToken(user._id),
      user: {
        id: user._id,
        name: user.name,
        email: user.email,
        role: user.role,
        status: user.status,
        isEmailVerified: user.isEmailVerified,
      },
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: "Server error during registration",
      error: error.message,
    });
  }
};

// @desc    Admin Login - Step 1: Validate credentials and send 2FA code
// @route   POST /api/auth/login-admin
// @access  Public
const loginAdmin = async (req, res) => {
  try {
    const { email, password } = req.body;

    // Validate input
    if (!email || !password) {
      return res.status(400).json({
        success: false,
        message: "Email and password are required",
      });
    }

    // Find admin user with password field
    const user = await User.findOne({ email, role: "admin" }).select(
      "+password"
    );
    if (!user) {
      return res.status(401).json({
        success: false,
        message: "Invalid credentials",
      });
    }

    // Check if user is active
    if (user.status === "banned") {
      return res.status(403).json({
        success: false,
        message: "Account is banned",
      });
    }

    if (user.status === "inactive") {
      return res.status(403).json({
        success: false,
        message: "Account is inactive. Please contact administrator.",
      });
    }

    // Check if password is set (for staff invitation flow)
    if (!user.isPasswordSet || !user.password) {
      return res.status(400).json({
        success: false,
        message: "Password not set. Please complete your account setup.",
      });
    }

    // Check password
    const isMatch = await bcrypt.compare(password, user.password);
    if (!isMatch) {
      return res.status(401).json({
        success: false,
        message: "Invalid credentials",
      });
    }

    // Generate and save two-factor code
    const twoFactorCode = generateTwoFactorCode();
    user.twoFactorCode = twoFactorCode;
    user.twoFactorExpires = Date.now() + 10 * 60 * 1000; // 10 minutes
    await user.save();

    // Send two-factor code
    const codeSent = await sendTwoFactorCode(user, twoFactorCode);
    if (!codeSent) {
      return res.status(500).json({
        success: false,
        message: "Failed to send verification code. Please try again.",
      });
    }

    res.json({
      success: true,
      message: "Credentials verified. Two-factor verification code sent.",
      requiresTwoFactor: true,
      userId: user._id,
      email: user.email,
      role: user.role,
    });
  } catch (error) {
    console.error("Admin login error:", error);
    res.status(500).json({
      success: false,
      message: "Server error during login",
      error: error.message,
    });
  }
};

// @desc    Employee Login - Step 1: Validate credentials and send 2FA code
// @route   POST /api/auth/login-employee
// @access  Public
const loginEmployee = async (req, res) => {
  try {
    const { email, password } = req.body;

    // Validate input
    if (!email || !password) {
      return res.status(400).json({
        success: false,
        message: "Email and password are required",
      });
    }

    // Find employee user with password field
    const user = await User.findOne({ email, role: "employee" }).select(
      "+password"
    );
    if (!user) {
      return res.status(401).json({
        success: false,
        message: "Invalid credentials",
      });
    }

    // Check if user is active
    if (user.status === "banned") {
      return res.status(403).json({
        success: false,
        message: "Account is banned",
      });
    }

    if (user.status === "inactive") {
      return res.status(403).json({
        success: false,
        message: "Account is inactive. Please contact administrator.",
      });
    }

    // Check if password is set (for staff invitation flow)
    if (!user.isPasswordSet || !user.password) {
      return res.status(400).json({
        success: false,
        message: "Password not set. Please complete your account setup.",
      });
    }

    // Check password
    const isMatch = await bcrypt.compare(password, user.password);
    if (!isMatch) {
      return res.status(401).json({
        success: false,
        message: "Invalid credentials",
      });
    }

    // Generate and save two-factor code
    const twoFactorCode = generateTwoFactorCode();
    user.twoFactorCode = twoFactorCode;
    user.twoFactorExpires = Date.now() + 10 * 60 * 1000; // 10 minutes
    await user.save();

    // Send two-factor code
    const codeSent = await sendTwoFactorCode(user, twoFactorCode);
    if (!codeSent) {
      return res.status(500).json({
        success: false,
        message: "Failed to send verification code. Please try again.",
      });
    }

    res.json({
      success: true,
      message: "Credentials verified. Two-factor verification code sent.",
      requiresTwoFactor: true,
      userId: user._id,
      email: user.email,
      role: user.role,
    });
  } catch (error) {
    console.error("Employee login error:", error);
    res.status(500).json({
      success: false,
      message: "Server error during login",
      error: error.message,
    });
  }
};

// @desc    Two-Factor Verification - Step 2: Verify code and issue JWT token
// @route   POST /api/auth/verify-code
// @access  Public
const verifyTwoFactorCode = async (req, res) => {
  try {
    const { userId, code } = req.body;

    // Validate input
    if (!userId || !code) {
      return res.status(400).json({
        success: false,
        message: "User ID and verification code are required",
      });
    }

    // Find user
    const user = await User.findById(userId);
    if (!user) {
      return res.status(404).json({
        success: false,
        message: "User not found",
      });
    }

    // Check if code exists and hasn't expired
    if (!user.twoFactorCode || !user.twoFactorExpires) {
      return res.status(400).json({
        success: false,
        message: "No verification code found. Please login again.",
      });
    }

    if (Date.now() > user.twoFactorExpires) {
      // Clear expired code
      user.twoFactorCode = undefined;
      user.twoFactorExpires = undefined;
      await user.save();

      return res.status(400).json({
        success: false,
        message: "Verification code has expired. Please login again.",
      });
    }

    // Verify code
    if (user.twoFactorCode !== code) {
      return res.status(400).json({
        success: false,
        message: "Invalid verification code",
      });
    }

    // Clear two-factor code
    user.twoFactorCode = undefined;
    user.twoFactorExpires = undefined;
    await user.save();

    // Generate JWT token (valid for 3 days)
    const token = generateToken(user._id);

    res.json({
      success: true,
      message: "Two-factor verification successful",
      token,
      user: {
        id: user._id,
        name: user.name,
        email: user.email,
        role: user.role,
        status: user.status,
        isEmailVerified: user.isEmailVerified,
        department: user.department,
      },
    });
  } catch (error) {
    console.error("Two-factor verification error:", error);
    res.status(500).json({
      success: false,
      message: "Server error during verification",
      error: error.message,
    });
  }
};

// @desc    Verify email
// @route   GET /api/users/verify-email/:token
// @access  Public
const verifyEmail = async (req, res) => {
  try {
    const { token } = req.params;

    const user = await User.findOne({
      emailVerificationToken: token,
      emailVerificationExpires: { $gt: Date.now() },
    });

    if (!user) {
      return res.status(400).json({
        success: false,
        message: "Invalid or expired verification token",
      });
    }

    user.isEmailVerified = true;
    user.emailVerificationToken = undefined;
    user.emailVerificationExpires = undefined;
    await user.save();

    res.json({
      success: true,
      message: "Email verified successfully",
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: "Server error during email verification",
      error: error.message,
    });
  }
};

// @desc    Get user profile
// @route   GET /api/users/profile
// @access  Private
const getUserProfile = async (req, res) => {
  try {
    const user = await User.findById(req.user.id);
    if (!user) {
      return res.status(404).json({
        success: false,
        message: "User not found",
      });
    }

    res.json({
      success: true,
      user: {
        id: user._id,
        name: user.name,
        email: user.email,
        phone: user.phone,
        role: user.role,
        department: user.department,
        status: user.status,
        shippingAddress: user.shippingAddress,
        profileImage: user.profileImage,
        paymentMethod: user.paymentMethod,
        isEmailVerified: user.isEmailVerified,
        joinedDate: user.joinedDate,
        createdAt: user.createdAt,
        updatedAt: user.updatedAt,
      },
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: "Server error fetching profile",
      error: error.message,
    });
  }
};

// @desc    Update user profile
// @route   PUT /api/users/profile
// @access  Private
const updateUserProfile = async (req, res) => {
  try {
    const { name, phone, department, shippingAddress, paymentMethod } =
      req.body;
    const user = await User.findById(req.user.id);
    if (!user) {
      return res.status(404).json({
        success: false,
        message: "User not found",
      });
    }
    if (req.file) {
      user.profileImage = { url: `/uploads/${req.file.filename}` };
    }
    if (name) user.name = name;
    if (phone) user.phone = phone;
    if (department) user.department = department;
    if (shippingAddress)
      user.shippingAddress = { ...user.shippingAddress, ...shippingAddress };
    if (paymentMethod) user.paymentMethod = paymentMethod;
    await user.save();
    res.json({
      success: true,
      message: "Profile updated successfully",
      user: {
        id: user._id,
        name: user.name,
        email: user.email,
        phone: user.phone,
        role: user.role,
        department: user.department,
        status: user.status,
        shippingAddress: user.shippingAddress,
        paymentMethod: user.paymentMethod,
        profileImage: user.profileImage,
        updatedAt: user.updatedAt,
      },
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: "Server error updating profile",
      error: error.message,
    });
  }
};

// @desc    Change password
// @route   PUT /api/users/change-password
// @access  Private
const changePassword = async (req, res) => {
  try {
    const { currentPassword, newPassword } = req.body;

    const user = await User.findById(req.user.id).select("+password");
    if (!user) {
      return res.status(404).json({
        success: false,
        message: "User not found",
      });
    }

    // Check current password
    const isMatch = await bcrypt.compare(currentPassword, user.password);
    if (!isMatch) {
      return res.status(400).json({
        success: false,
        message: "Current password is incorrect",
      });
    }

    // Hash new password
    const salt = await bcrypt.genSalt(10);
    user.password = await bcrypt.hash(newPassword, salt);
    await user.save();

    res.json({
      success: true,
      message: "Password changed successfully",
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: "Server error changing password",
      error: error.message,
    });
  }
};

// @desc    Forgot password
// @route   POST /api/users/forgot-password
// @access  Public
const forgotPassword = async (req, res) => {
  try {
    const { email } = req.body;

    const user = await User.findOne({ email });
    if (!user) {
      return res.status(404).json({
        success: false,
        message: "User not found with this email",
      });
    }

    // Generate reset token
    const resetToken = crypto.randomBytes(32).toString("hex");
    user.passwordResetToken = resetToken;
    user.passwordResetExpires = Date.now() + 60 * 60 * 1000; // 1 hour
    await user.save();

    // Send branded reset email
    const resetUrl = `${
      process.env.FRONTEND_URL || "http://localhost:8081"
    }/auth/reset-password/${resetToken}`;
    try {
      await sendPasswordResetEmail(email, user.name, resetUrl);
      console.log(`✅ Password reset email sent to ${email}`);
    } catch (emailError) {
      console.error("Failed to send password reset email:", emailError);
      // Don't fail the request if email fails
    }

    res.json({
      success: true,
      message: "Password reset link sent to your email",
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: "Server error sending reset email",
      error: error.message,
    });
  }
};

// @desc    Reset password
// @route   POST /api/auth/reset-password/:token
// @access  Public
const resetPassword = async (req, res) => {
  try {
    const { token } = req.params;
    const { password } = req.body;

    const user = await User.findOne({
      passwordResetToken: token,
      passwordResetExpires: { $gt: Date.now() },
    });

    if (!user) {
      return res.status(400).json({
        success: false,
        message: "Invalid or expired reset token",
      });
    }

    // Hash new password
    const salt = await bcrypt.genSalt(10);
    user.password = await bcrypt.hash(password, salt);
    user.isPasswordSet = true; // Ensure password is marked as set
    user.passwordResetToken = undefined;
    user.passwordResetExpires = undefined;
    await user.save();

    res.json({
      success: true,
      message: "Password reset successfully",
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: "Server error resetting password",
      error: error.message,
    });
  }
};

// @desc    Get all users (Admin only)
// @route   GET /api/users
// @access  Private/Admin
const getAllUsers = async (req, res) => {
  try {
    const { page = 1, limit = 10, role, status, search } = req.query;

    const query = {};
    if (role) query.role = role;
    if (status) query.status = status;
    if (search) {
      query.$or = [
        { name: { $regex: search, $options: "i" } },
        { email: { $regex: search, $options: "i" } },
      ];
    }

    const users = await User.find(query)
      .select("-password -emailVerificationToken -passwordResetToken")
      .limit(limit * 1)
      .skip((page - 1) * limit)
      .sort({ createdAt: -1 });

    const total = await User.countDocuments(query);

    res.json({
      success: true,
      users,
      pagination: {
        page: parseInt(page),
        limit: parseInt(limit),
        total,
        pages: Math.ceil(total / limit),
      },
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: "Server error fetching users",
      error: error.message,
    });
  }
};

// @desc    Get user by ID (Admin only)
// @route   GET /api/users/:id
// @access  Private/Admin
const getUserById = async (req, res) => {
  try {
    const user = await User.findById(req.params.id).select(
      "-password -emailVerificationToken -passwordResetToken"
    );

    if (!user) {
      return res.status(404).json({
        success: false,
        message: "User not found",
      });
    }

    res.json({
      success: true,
      user,
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: "Server error fetching user",
      error: error.message,
    });
  }
};

// @desc    Update user status (Admin only)
// @route   PUT /api/users/:id/status
// @access  Private/Admin
const updateUserStatus = async (req, res) => {
  try {
    const { status } = req.body;

    if (!["active", "inactive", "banned"].includes(status)) {
      return res.status(400).json({
        success: false,
        message: "Invalid status value",
      });
    }

    const user = await User.findByIdAndUpdate(
      req.params.id,
      { status },
      { new: true }
    ).select("-password -emailVerificationToken -passwordResetToken");

    if (!user) {
      return res.status(404).json({
        success: false,
        message: "User not found",
      });
    }

    res.json({
      success: true,
      message: "User status updated successfully",
      user,
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: "Server error updating user status",
      error: error.message,
    });
  }
};

// @desc    Delete user (Admin only)
// @route   DELETE /api/users/:id
// @access  Private/Admin
const deleteUser = async (req, res) => {
  try {
    const user = await User.findByIdAndDelete(req.params.id);

    if (!user) {
      return res.status(404).json({
        success: false,
        message: "User not found",
      });
    }

    res.json({
      success: true,
      message: "User deleted successfully",
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: "Server error deleting user",
      error: error.message,
    });
  }
};

// @desc    Set password for invited staff member
// @route   POST /api/auth/staff/set-password
// @access  Public (for invited staff)
const setStaffPassword = async (req, res) => {
  try {
    const { email, password, confirmPassword, firstName, lastName } = req.body;

    // Validate required fields
    if (!email || !password || !confirmPassword || !firstName || !lastName) {
      return res.status(400).json({
        success: false,
        message: "Please provide all required fields",
      });
    }

    // Validate password match
    if (password !== confirmPassword) {
      return res.status(400).json({
        success: false,
        message: "Passwords do not match",
      });
    }

    // Validate password strength
    if (password.length < 6) {
      return res.status(400).json({
        success: false,
        message: "Password must be at least 6 characters long",
      });
    }

    // Find user by email (should exist from staff invitation)
    const user = await User.findOne({ email }).select("+password");

    if (!user) {
      return res.status(404).json({
        success: false,
        message: "No invitation found for this email address",
      });
    }

    // Check if password is already set
    if (user.isPasswordSet) {
      return res.status(400).json({
        success: false,
        message: "Password has already been set for this account",
      });
    }

    // Hash the new password
    const hashedPassword = await bcrypt.hash(password, 10);

    // Update user with password and name
    user.password = hashedPassword;
    user.isPasswordSet = true;
    user.isEmailVerified = true;
    user.name = `${firstName} ${lastName}`;
    await user.save();

    // Generate JWT token
    const token = generateToken(user._id);

    // Return success response with token
    res.status(200).json({
      success: true,
      message: "Password set successfully. Welcome to Dr. Kumar Admin Panel!",
      data: {
        token,
        user: {
          id: user._id,
          name: user.name,
          email: user.email,
          role: user.role,
          department: user.department,
          status: user.status,
        },
      },
    });
  } catch (error) {
    console.error("Set staff password error:", error);
    res.status(500).json({
      success: false,
      message: "Server error while setting password",
      error: error.message,
    });
  }
};

module.exports = {
  registerUser,
  loginAdmin,
  loginEmployee,
  verifyTwoFactorCode,
  verifyEmail,
  getUserProfile,
  updateUserProfile,
  changePassword,
  forgotPassword,
  resetPassword,
  getAllUsers,
  getUserById,
  updateUserStatus,
  deleteUser,
  setStaffPassword,
};
