const mongoose = require('mongoose');
require('dotenv').config();

// Import existing models
const Product = require('./models/Products');
const User = require('./models/User');

async function createTestReviews() {
    try {
        // Connect to database
        await mongoose.connect(process.env.DB_URL, {
            dbName: process.env.DB_NAME || 'ecommerce',
        });
        console.log('✅ Connected to MongoDB');

        // Get existing products and users
        const products = await Product.find({});
        const users = await User.find({});

        if (products.length === 0) {
            console.log('❌ No products found. Cannot create reviews without products.');
            return;
        }

        if (users.length === 0) {
            console.log('❌ No users found. Cannot create reviews without users.');
            return;
        }

        console.log(`📦 Found ${products.length} products`);
        console.log(`👥 Found ${users.length} users`);

        // Check existing reviews
        const productsWithReviews = await Product.find({ 'reviews.0': { $exists: true } });
        console.log(`📊 Found ${productsWithReviews.length} products with existing reviews`);

        if (productsWithReviews.length > 0) {
            console.log('✅ Reviews already exist. Displaying existing reviews:');
            
            let totalReviews = 0;
            productsWithReviews.forEach((product, index) => {
                console.log(`   ${index + 1}. Product: ${product.name} - Reviews: ${product.reviews.length}`);
                totalReviews += product.reviews.length;
            });
            console.log(`📊 Total reviews: ${totalReviews}`);
            return;
        }

        // Create test reviews for products
        const testReviews = [
            {
                rating: 5,
                comment: "Excellent product! Really helped with my health issues. Highly recommend to everyone.",
                status: "approved"
            },
            {
                rating: 4,
                comment: "Good quality product. Saw improvements after using for a few weeks. Will order again.",
                status: "approved"
            },
            {
                rating: 5,
                comment: "Amazing results! This product exceeded my expectations. Fast delivery too.",
                status: "approved"
            },
            {
                rating: 3,
                comment: "It's okay, but didn't see dramatic changes. Maybe need to use longer.",
                status: "pending"
            },
            {
                rating: 4,
                comment: "Very satisfied with the quality. Good packaging and authentic product.",
                status: "approved"
            },
            {
                rating: 2,
                comment: "Not what I expected. The taste is too strong and hard to consume.",
                status: "rejected"
            },
            {
                rating: 5,
                comment: "Perfect! This has become part of my daily routine. Great value for money.",
                status: "approved"
            },
            {
                rating: 4,
                comment: "Good product overall. Noticed positive effects on my energy levels.",
                status: "pending"
            },
            {
                rating: 1,
                comment: "Terrible experience. Product arrived damaged and customer service was unhelpful.",
                status: "rejected"
            },
            {
                rating: 5,
                comment: "Outstanding quality! Dr. Kumar's products are always reliable and effective.",
                status: "approved"
            }
        ];

        let reviewsCreated = 0;

        // Add reviews to products
        for (let i = 0; i < Math.min(products.length, 5); i++) {
            const product = products[i];
            const numReviews = Math.floor(Math.random() * 3) + 1; // 1-3 reviews per product

            for (let j = 0; j < numReviews; j++) {
                const randomUser = users[Math.floor(Math.random() * users.length)];
                const randomReview = testReviews[Math.floor(Math.random() * testReviews.length)];

                // Check if user already reviewed this product
                const existingReview = product.reviews.find(
                    review => review.user.toString() === randomUser._id.toString()
                );

                if (!existingReview) {
                    const review = {
                        user: randomUser._id,
                        rating: randomReview.rating,
                        comment: randomReview.comment,
                        status: randomReview.status,
                        createdAt: new Date(Date.now() - Math.random() * 30 * 24 * 60 * 60 * 1000), // Random date within last 30 days
                        updatedAt: new Date()
                    };

                    product.reviews.push(review);
                    reviewsCreated++;
                }
            }

            // Update review statistics
            await product.updateReviewStats();
            console.log(`   ✅ Added reviews to: ${product.name} (${product.reviews.length} total reviews)`);
        }

        console.log(`\n✅ Created ${reviewsCreated} test reviews successfully!`);

        // Display summary
        const updatedProducts = await Product.find({ 'reviews.0': { $exists: true } })
            .populate('reviews.user', 'name email');

        console.log('\n📊 Review Summary:');
        let totalReviewsCreated = 0;
        updatedProducts.forEach((product, index) => {
            console.log(`   ${index + 1}. ${product.name}:`);
            console.log(`      - Total Reviews: ${product.reviews.length}`);
            console.log(`      - Average Rating: ${product.averageRating.toFixed(1)}`);
            
            product.reviews.forEach((review, reviewIndex) => {
                console.log(`         ${reviewIndex + 1}. ${review.rating}⭐ by ${review.user?.name} - ${review.status}`);
            });
            totalReviewsCreated += product.reviews.length;
        });

        console.log(`\n💡 Total reviews created: ${totalReviewsCreated}`);
        console.log('🎉 Test reviews created! You can now test the Review Management page.');

    } catch (error) {
        console.error('❌ Error:', error.message);
    } finally {
        await mongoose.disconnect();
        console.log('🔌 Disconnected from MongoDB');
    }
}

createTestReviews();
