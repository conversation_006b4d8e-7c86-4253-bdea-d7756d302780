const axios = require('axios');

const BASE_URL = 'http://localhost:5000';

async function testComplaintsEndpoint() {
    console.log('🧪 Testing Complaints Endpoint...\n');

    try {
        // Test 1: Check if complaints endpoint exists (without auth)
        console.log('1. Testing complaints endpoint without authentication...');
        try {
            const response = await axios.get(`${BASE_URL}/api/admin/complaints`);
            console.log('❌ Complaints endpoint should require authentication');
        } catch (error) {
            if (error.response && error.response.status === 401) {
                console.log('✅ Complaints endpoint properly requires authentication');
                console.log('   Status:', error.response.status);
                console.log('   Message:', error.response.data?.message);
            } else {
                console.log('❓ Unexpected error:', error.message);
            }
        }

        // Test 2: Test with mock token to see endpoint structure
        console.log('\n2. Testing complaints endpoint structure...');
        try {
            const response = await axios.get(`${BASE_URL}/api/admin/complaints`, {
                headers: {
                    'Authorization': 'Bearer mock-token'
                }
            });
            console.log('✅ Complaints endpoint response:', response.data);
        } catch (error) {
            if (error.response) {
                console.log('📋 Complaints endpoint error (expected due to auth):');
                console.log('   Status:', error.response.status);
                console.log('   Message:', error.response.data?.message);
                
                // Check if it's a token validation error (good) vs route not found (bad)
                if (error.response.status === 401 || error.response.status === 403) {
                    console.log('✅ Endpoint exists and is properly protected');
                } else if (error.response.status === 404) {
                    console.log('❌ Endpoint not found - route may not be configured');
                }
            } else {
                console.log('❌ Network error:', error.message);
            }
        }

        // Test 3: Check if the route is registered
        console.log('\n3. Testing if admin routes are accessible...');
        try {
            const response = await axios.get(`${BASE_URL}/api/admin/`);
            console.log('✅ Admin routes are accessible');
        } catch (error) {
            if (error.response && error.response.status === 404) {
                console.log('❌ Admin routes not found - check route configuration');
            } else {
                console.log('📋 Admin routes response:', error.response?.status, error.response?.data?.message);
            }
        }

        // Test 4: Test basic server health
        console.log('\n4. Testing server health...');
        try {
            const response = await axios.get(`${BASE_URL}/`);
            console.log('✅ Server is responding');
            console.log('   Response:', response.data);
        } catch (error) {
            console.log('❌ Server health check failed:', error.message);
        }

        console.log('\n💡 Endpoint testing completed');

    } catch (error) {
        console.error('❌ Test failed:', error.message);
    }
}

// Run the test
testComplaintsEndpoint();
