import { useState, useEffect } from "react";
import { <PERSON>, <PERSON>Content, Card<PERSON><PERSON>er, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Input } from "@/components/ui/input";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Plus,
  Download,
  RefreshCw,
  AlertTriangle,
  Edit,
  Package,
  Lock,
} from "lucide-react";
import { useToast } from "@/hooks/use-toast";
import { usePagination } from "@/hooks/use-pagination";
import { TablePagination } from "@/components/ui/table-pagination";
import AddPurchaseModal from "./AddPurchaseModal";
import BatchHistoryModal from "./BatchHistoryModal";
import InventoryEditModal from "./InventoryEditModal";
import productsAPI from "@/api/products.api.js";
import { apiHelpers } from "@/utils/apiHelpers";
import useRolePermissions from "@/hooks/useRolePermissions";

// Interface for inventory item
interface InventoryItem {
  id: string;
  product: string;
  sku: string;
  category: string;
  image?: string;
  currentStock: number;
  minStock: number;
  stockLevel: string;
  value: string;
  price: string;
  lastRestocked: string;
  status: string;
  batches: string;
}

const Inventory = () => {
  const { toast } = useToast();
  const permissions = useRolePermissions();
  const [inventory, setInventory] = useState<InventoryItem[]>([]);
  const [loading, setLoading] = useState(true);
  const [isAddPurchaseOpen, setIsAddPurchaseOpen] = useState(false);
  const [isBatchHistoryOpen, setIsBatchHistoryOpen] = useState(false);
  const [isEditModalOpen, setIsEditModalOpen] = useState(false);
  const [selectedProduct, setSelectedProduct] = useState(null);
  const [editingProduct, setEditingProduct] = useState(null);
  const [isRefreshing, setIsRefreshing] = useState(false);
  const [searchTerm, setSearchTerm] = useState("");
  const [statusFilter, setStatusFilter] = useState("all");

  // Statistics state
  const [stats, setStats] = useState({
    totalItems: 0,
    lowStock: 0,
    outOfStock: 0,
    totalValue: "₹0",
  });

  // Fetch inventory from API
  const fetchInventory = async (showLoading = true) => {
    try {
      if (showLoading) setLoading(true);

      const filters = {
        search: searchTerm,
        category: statusFilter !== "all" ? statusFilter : "",
        limit: 100,
      };

      const response = await productsAPI.getProducts(filters);

      if (apiHelpers.isSuccessResponse(response)) {
        const data = apiHelpers.extractData(response);
        const productsData = data.products || [];

        // Format products for inventory display
        const formattedInventory = productsData.map((product: any) =>
          productsAPI.formatProductForInventory(product)
        );

        setInventory(formattedInventory);

        // Calculate statistics
        const newStats = {
          totalItems: formattedInventory.length,
          lowStock: formattedInventory.filter(
            (item: InventoryItem) => item.status === "low stock"
          ).length,
          outOfStock: formattedInventory.filter(
            (item: InventoryItem) => item.status === "out of stock"
          ).length,
          totalValue: formattedInventory
            .reduce((total: number, item: InventoryItem) => {
              const value = parseFloat(item.value.replace(/[₹,]/g, ""));
              return total + value;
            }, 0)
            .toLocaleString("en-IN", { style: "currency", currency: "INR" }),
        };

        setStats(newStats);
      } else {
        throw new Error(response.message || "Failed to fetch inventory");
      }
    } catch (error: any) {
      console.error("Error fetching inventory:", error);
      apiHelpers.handleError(error, "Failed to fetch inventory data");
    } finally {
      setLoading(false);
      setIsRefreshing(false);
    }
  };

  // Refresh inventory data
  const handleRefresh = async () => {
    setIsRefreshing(true);
    await fetchInventory(false);
  };

  // Load inventory data on component mount and when filters change
  useEffect(() => {
    fetchInventory();
  }, [searchTerm, statusFilter]);

  // Filter inventory based on search and status
  const filteredInventory = inventory.filter((item) => {
    const matchesSearch =
      item.product.toLowerCase().includes(searchTerm.toLowerCase()) ||
      item.sku.toLowerCase().includes(searchTerm.toLowerCase()) ||
      item.category.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesStatus =
      statusFilter === "all" ||
      item.status.toLowerCase() === statusFilter.toLowerCase();

    return matchesSearch && matchesStatus;
  });

  // Pagination logic
  const {
    currentPage,
    totalPages,
    paginatedData: paginatedInventory,
    goToPage,
    totalItems,
  } = usePagination({
    data: filteredInventory,
    itemsPerPage: 10,
  });

  const handleExportReport = () => {
    const csvContent = [
      [
        "Product",
        "SKU",
        "Category",
        "Current Stock",
        "Min Stock",
        "Value",
        "Price",
        "Last Restocked",
        "Status",
      ].join(","),
      ...inventory.map((item) =>
        [
          item.product,
          item.sku,
          item.category,
          item.currentStock,
          item.minStock,
          item.value,
          item.price,
          item.lastRestocked,
          item.status,
        ].join(",")
      ),
    ].join("\n");

    const blob = new Blob([csvContent], { type: "text/csv" });
    const url = URL.createObjectURL(blob);
    const a = document.createElement("a");
    a.href = url;
    a.download = `inventory-report-${
      new Date().toISOString().split("T")[0]
    }.csv`;
    a.click();
    URL.revokeObjectURL(url);

    toast({
      title: "Success",
      description: "Inventory report downloaded successfully",
    });
  };

  const handleAddPurchase = (inventoryData: any) => {
    const newInventoryItem = {
      id: Date.now(),
      product: inventoryData.product,
      sku: inventoryData.sku || `KAP-${Date.now()}`, // Generate SKU if not provided
      category: inventoryData.category || "General",
      image:
        inventoryData.image ||
        "https://images.unsplash.com/photo-1544787219-7f47ccb76574?w=100&h=100&fit=crop&crop=center",
      currentStock: inventoryData.currentStock,
      minStock: inventoryData.minStock || 10, // Default minimum stock
      stockLevel: inventoryData.stockLevel,
      value: inventoryData.value,
      price: inventoryData.price || "₹0", // Default price if not provided
      lastRestocked: inventoryData.lastRestocked,
      status: inventoryData.status,
      batches: "1", // Convert to string to match existing data type
    };

    setInventory((prev) => [...prev, newInventoryItem]);

    toast({
      title: "Inventory Item Added",
      description: `${inventoryData.product} has been added to inventory successfully`,
    });
  };

  const handleViewBatches = (productName: string) => {
    setSelectedProduct(productName);
    setIsBatchHistoryOpen(true);
  };

  const handleEditProduct = (product) => {
    setEditingProduct(product);
    setIsEditModalOpen(true);
  };

  const handleUpdateProduct = (updatedProduct: any) => {
    setInventory((prev) =>
      prev.map((item) =>
        item.id === updatedProduct.id ? { ...item, ...updatedProduct } : item
      )
    );
    toast({
      title: "Product Updated",
      description: "Product details have been successfully updated",
    });
  };

  const handleStatusChange = (productId, newStatus) => {
    setInventory((prev) =>
      prev.map((item) =>
        item.id === productId ? { ...item, status: newStatus } : item
      )
    );
    toast({
      title: "Status Updated",
      description: "Product status has been successfully updated",
    });
  };

  const handleStatusToggle = (productId, currentStatus) => {
    const statusCycle = ["in stock", "out of stock", "low stock"];
    const currentIndex = statusCycle.indexOf(currentStatus);
    const nextStatus = statusCycle[(currentIndex + 1) % statusCycle.length];
    handleStatusChange(productId, nextStatus);
  };

  const getStatusBadgeColor = (status) => {
    switch (status) {
      case "in stock":
        return "bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200";
      case "out of stock":
        return "bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200";
      case "low stock":
        return "bg-orange-100 text-orange-800 dark:bg-orange-900 dark:text-orange-200";
      default:
        return "bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200";
    }
  };

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between pt-2">
        <div>
          <h1 className="text-3xl font-bold text-gray-900 dark:text-white transition-colors duration-500 ease-in-out">
            Inventory Management
          </h1>
          <p className="text-base text-gray-600 dark:text-gray-300 transition-colors duration-500 ease-in-out">
            Monitor stock levels, batches, and purchase entries
          </p>
        </div>
        <div className="flex gap-3">
          {permissions.inventory.canAdd ? (
            <Button
              className="bg-green-600 hover:bg-green-700 text-white text-base px-6 py-3"
              onClick={() => setIsAddPurchaseOpen(true)}
            >
              <Plus className="w-6 h-6 mr-2" />
              Add Inventory
            </Button>
          ) : (
            <Button
              className="bg-gray-400 text-white text-base px-6 py-3 cursor-not-allowed"
              disabled
              title="Only administrators can add inventory items"
            >
              <Lock className="w-6 h-6 mr-2" />
              Add Inventory (Admin Only)
            </Button>
          )}
          <Button
            variant="outline"
            onClick={handleExportReport}
            className="text-base px-6 py-3"
          >
            <Download className="w-6 h-6 mr-2" />
            Export
          </Button>
          <Button
            variant="outline"
            onClick={handleRefresh}
            disabled={isRefreshing}
            className="text-base px-6 py-3"
          >
            <RefreshCw
              className={`w-6 h-6 mr-2 ${isRefreshing ? "animate-spin" : ""}`}
            />
            {isRefreshing ? "Refreshing..." : "Refresh"}
          </Button>
        </div>
      </div>

      {/* Inventory Statistics */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <Card className="transition-colors duration-500 ease-in-out bg-white dark:bg-gray-800 border-gray-200 dark:border-gray-700">
          <CardContent className="p-6">
            <div className="space-y-2">
              <p className="text-lg font-medium transition-colors duration-500 ease-in-out text-gray-600 dark:text-gray-400">
                Total Items
              </p>
              <p className="text-3xl font-bold text-black dark:text-white">
                {stats.totalItems}
              </p>
            </div>
          </CardContent>
        </Card>

        <Card className="transition-colors duration-500 ease-in-out bg-white dark:bg-gray-800 border-gray-200 dark:border-gray-700">
          <CardContent className="p-6">
            <div className="space-y-2">
              <p className="text-lg font-medium transition-colors duration-500 ease-in-out text-gray-600 dark:text-gray-400">
                Low Stock
              </p>
              <p className="text-3xl font-bold text-red-600">
                {stats.lowStock}
              </p>
            </div>
          </CardContent>
        </Card>

        <Card className="transition-colors duration-500 ease-in-out bg-white dark:bg-gray-800 border-gray-200 dark:border-gray-700">
          <CardContent className="p-6">
            <div className="space-y-2">
              <p className="text-lg font-medium transition-colors duration-500 ease-in-out text-gray-600 dark:text-gray-400">
                Out of Stock
              </p>
              <p className="text-3xl font-bold text-red-600">
                {stats.outOfStock}
              </p>
            </div>
          </CardContent>
        </Card>

        <Card className="transition-colors duration-500 ease-in-out bg-white dark:bg-gray-800 border-gray-200 dark:border-gray-700">
          <CardContent className="p-6">
            <div className="space-y-2">
              <p className="text-lg font-medium transition-colors duration-500 ease-in-out text-gray-600 dark:text-gray-400">
                Total Value
              </p>
              <p className="text-3xl font-bold text-blue-600">
                {stats.totalValue}
              </p>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Dynamic Low Stock Alert */}
      {stats.lowStock > 0 && (
        <Card className="border-orange-200 bg-orange-50 dark:bg-orange-900/20">
          <CardContent className="p-6">
            <div className="flex items-center gap-4">
              <AlertTriangle className="w-8 h-8 text-orange-600" />
              <div className="flex-1">
                <h3 className="font-medium text-orange-800 dark:text-orange-200 text-xl">
                  Low Stock Alert
                </h3>
                <p className="text-lg text-orange-700 dark:text-orange-300">
                  {stats.lowStock} item(s) need restocking
                </p>
              </div>
            </div>
            <div className="mt-4 space-y-2">
              {inventory
                .filter((item) => item.status === "low stock")
                .slice(0, 5) // Show only first 5 items to avoid clutter
                .map((item) => (
                  <div
                    key={item.id}
                    className="p-3 bg-white dark:bg-gray-800 rounded border border-orange-200"
                  >
                    <p className="text-lg font-medium text-orange-800 dark:text-orange-200">
                      {item.product} ({item.currentStock} left)
                    </p>
                    <p className="text-sm text-orange-600 dark:text-orange-400">
                      SKU: {item.sku} | Min Stock: {item.minStock}
                    </p>
                  </div>
                ))}
              {inventory.filter((item) => item.status === "low stock").length >
                5 && (
                <div className="p-3 bg-white dark:bg-gray-800 rounded border border-orange-200">
                  <p className="text-sm text-orange-600 dark:text-orange-400">
                    ... and{" "}
                    {inventory.filter((item) => item.status === "low stock")
                      .length - 5}{" "}
                    more items
                  </p>
                </div>
              )}
            </div>
          </CardContent>
        </Card>
      )}

      {/* Filters */}
      <Card className="transition-colors duration-500 ease-in-out bg-white dark:bg-gray-800 border-gray-200 dark:border-gray-700">
        <CardContent className="p-6">
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-2 transition-colors duration-500 ease-in-out text-gray-700 dark:text-gray-300">
                <span className="font-medium text-xl">
                  🔍 Search & Filter Inventory
                </span>
              </div>
              <Button variant="outline" className="text-base">
                Clear Filters
              </Button>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div>
                <label className="block text-lg font-medium transition-colors duration-500 ease-in-out text-gray-700 dark:text-gray-300 mb-2">
                  Search Products
                </label>
                <Input
                  placeholder="Search by name or SKU..."
                  className="transition-colors duration-500 ease-in-out bg-white dark:bg-gray-700 border-gray-300 dark:border-gray-600 text-base"
                />
              </div>
              <div>
                <label className="block text-base font-medium transition-colors duration-500 ease-in-out text-gray-700 dark:text-gray-300 mb-2">
                  Status
                </label>
                <Select>
                  <SelectTrigger className="text-base">
                    <SelectValue placeholder="All Status" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All Status</SelectItem>
                    <SelectItem value="in-stock">In Stock</SelectItem>
                    <SelectItem value="low-stock">Low Stock</SelectItem>
                    <SelectItem value="out-of-stock">Out of Stock</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              <div>
                <label className="block text-base font-medium transition-colors duration-500 ease-in-out text-gray-700 dark:text-gray-300 mb-2">
                  Category
                </label>
                <Select>
                  <SelectTrigger className="text-base">
                    <SelectValue placeholder="All Categories" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All Categories</SelectItem>
                    <SelectItem value="health-drinks">Health Drinks</SelectItem>
                    <SelectItem value="supplements">Supplements</SelectItem>
                    <SelectItem value="combo-packs">Combo Packs</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>

            <div className="text-base text-gray-500 dark:text-gray-400">
              Showing inventory items
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Inventory Table */}
      <Card className="transition-colors duration-500 ease-in-out bg-white dark:bg-gray-800 border-gray-200 dark:border-gray-700">
        <CardHeader>
          <CardTitle className="transition-colors duration-500 ease-in-out text-gray-900 dark:text-white text-2xl">
            Inventory
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {/* Table Header */}
            <div className="grid grid-cols-7 gap-4 p-4 bg-gray-50 dark:bg-gray-700 rounded-lg font-medium text-gray-700 dark:text-gray-300 text-lg border-b border-gray-200 dark:border-gray-600">
              <div className="text-center">Image</div>
              <div className="text-center">Product</div>
              <div className="text-center">Current Stock</div>
              <div className="text-center">Value</div>
              <div className="text-center">Last Restocked</div>
              <div className="text-center">Status</div>
              <div className="text-center">Actions</div>
            </div>

            {/* Table Rows */}
            <div className="space-y-3">
              {loading ? (
                // Loading state
                Array.from({ length: 5 }).map((_, index) => (
                  <div
                    key={index}
                    className="grid grid-cols-7 gap-4 p-4 border border-gray-200 dark:border-gray-600 rounded-lg"
                  >
                    {Array.from({ length: 7 }).map((_, colIndex) => (
                      <div key={colIndex} className="space-y-2">
                        <div className="h-4 bg-gray-200 dark:bg-gray-700 rounded animate-pulse"></div>
                        <div className="h-3 bg-gray-200 dark:bg-gray-700 rounded animate-pulse"></div>
                      </div>
                    ))}
                  </div>
                ))
              ) : filteredInventory.length === 0 ? (
                // Empty state
                <div className="text-center py-12">
                  <Package className="w-16 h-16 mx-auto text-gray-400 mb-4" />
                  <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-2">
                    No inventory items found
                  </h3>
                  <p className="text-gray-500 dark:text-gray-400">
                    {searchTerm || statusFilter !== "all"
                      ? "Try adjusting your search or filters"
                      : "No products have been added to inventory yet"}
                  </p>
                </div>
              ) : (
                paginatedInventory.map((item, index) => (
                  <div
                    key={index}
                    className="grid grid-cols-7 gap-4 p-4 border border-gray-200 dark:border-gray-600 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700 items-center"
                  >
                    <div className="flex justify-center">
                      {item.image ? (
                        <img
                          src={item.image}
                          alt={item.product}
                          className="w-12 h-12 object-cover rounded-lg border border-gray-200 dark:border-gray-600"
                        />
                      ) : (
                        <div className="w-12 h-12 bg-gray-100 dark:bg-gray-600 rounded-lg flex items-center justify-center border border-gray-200 dark:border-gray-600">
                          <span className="text-gray-400 text-xs">
                            No Image
                          </span>
                        </div>
                      )}
                    </div>
                    <div className="text-center">
                      <div className="font-medium text-gray-900 dark:text-white text-lg">
                        {item.product}
                      </div>
                      <div className="text-base text-gray-500 dark:text-gray-400">
                        SKU: {item.sku}
                      </div>
                      <div className="text-base text-gray-500 dark:text-gray-400">
                        {item.category}
                      </div>
                    </div>
                    <div className="text-center">
                      <div className="font-medium text-gray-900 dark:text-white text-xl">
                        {item.currentStock}
                      </div>
                      <div className="text-base text-gray-500 dark:text-gray-400">
                        Min: {item.minStock}
                      </div>
                    </div>

                    <div className="text-center">
                      <div className="font-medium text-gray-900 dark:text-white text-xl">
                        {item.value}
                      </div>
                      <div className="text-base text-gray-500 dark:text-gray-400">
                        @ {item.price}
                      </div>
                    </div>
                    <div className="text-gray-600 dark:text-gray-300 text-lg text-center">
                      {item.lastRestocked}
                    </div>
                    <div className="flex justify-center">
                      <Button
                        variant="outline"
                        onClick={() => handleStatusToggle(item.id, item.status)}
                        className="w-full max-w-[140px] text-base bg-red-50 hover:bg-red-100 border-red-200 text-red-700 dark:bg-black dark:hover:bg-gray-800 dark:border-gray-600 dark:text-white"
                      >
                        <Badge
                          className={`${getStatusBadgeColor(
                            item.status
                          )} text-base`}
                        >
                          {item.status === "in stock"
                            ? "In Stock"
                            : item.status === "out of stock"
                            ? "Out of Stock"
                            : "Low Stock"}
                        </Badge>
                      </Button>
                    </div>

                    <div className="flex items-center justify-center gap-2">
                      {permissions.inventory.canEdit ? (
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => handleEditProduct(item)}
                          className="p-3"
                          title="Edit Inventory Item"
                        >
                          <Edit className="w-6 h-6" />
                        </Button>
                      ) : (
                        <Button
                          variant="ghost"
                          size="sm"
                          disabled
                          className="p-3 opacity-50 cursor-not-allowed"
                          title="Only administrators can edit inventory items"
                        >
                          <Lock className="w-5 h-5" />
                        </Button>
                      )}
                    </div>
                  </div>
                ))
              )}
            </div>

            {/* Pagination */}
            <TablePagination
              currentPage={currentPage}
              totalPages={totalPages}
              onPageChange={goToPage}
              totalItems={totalItems}
              itemsPerPage={10}
            />
          </div>
        </CardContent>
      </Card>

      <AddPurchaseModal
        isOpen={isAddPurchaseOpen}
        onClose={() => setIsAddPurchaseOpen(false)}
        onSubmit={handleAddPurchase}
      />

      <BatchHistoryModal
        isOpen={isBatchHistoryOpen}
        onClose={() => setIsBatchHistoryOpen(false)}
        productName={selectedProduct}
      />

      <InventoryEditModal
        isOpen={isEditModalOpen}
        onClose={() => setIsEditModalOpen(false)}
        product={editingProduct}
        onUpdate={handleUpdateProduct}
      />
    </div>
  );
};

export default Inventory;
