import React, { useState } from "react";
import { Link, useNavigate, useLocation } from "react-router-dom";
import { But<PERSON> } from "@/components/ui/button";
import { ArrowLeft } from "lucide-react";
import {
  InputOTP,
  InputOTPGroup,
  InputOTPSlot,
} from "@/components/ui/input-otp";
import { useToast } from "@/hooks/use-toast";
import { authService } from "@/services/authService";
import authAPI from "@/api/auth.api.js";

// Helper function to get role-based redirect path
const getRoleBasedRedirectPath = (userRole: string): string => {
  // Both admin and employee users go to dashboard
  // The individual pages will handle role-based action restrictions
  return "/dashboard";
};

const TwoFactorVerification = () => {
  const [otp, setOtp] = useState("");
  const [isLoading, setIsLoading] = useState(false);
  const navigate = useNavigate();
  const location = useLocation();
  const { toast } = useToast();

  const userId = location.state?.userId;
  const userEmail = location.state?.email || "<EMAIL>";
  const userRole = location.state?.userRole || location.state?.role || "admin";
  const userName = location.state?.userName || userRole;
  const keepLoggedIn = location.state?.keepLoggedIn || false;

  const handleVerification = async (e: React.FormEvent) => {
    e.preventDefault();

    if (otp.length !== 6) {
      toast({
        title: "Error",
        description: "Please enter a valid 6-digit code",
        variant: "destructive",
      });
      return;
    }

    if (!userId) {
      toast({
        title: "Error",
        description: "Session expired. Please login again.",
        variant: "destructive",
      });
      navigate("/auth/signin");
      return;
    }

    setIsLoading(true);

    try {
      // Use authAPI for consistency with the rest of the app
      const response = await authAPI.verifyTwoFactor({
        userId,
        code: otp,
      });

      if (response.success && response.data?.token) {
        // Handle token storage based on "Keep me logged in" preference
        const token = response.data.token;
        const user = response.data.user;

        if (keepLoggedIn) {
          // Store in localStorage for persistent login (3 days)
          localStorage.setItem("auth-token", token);
          localStorage.setItem("auth-user", JSON.stringify(user));
          localStorage.setItem("keep-logged-in", "true");
          // Remove from sessionStorage if exists
          sessionStorage.removeItem("auth-token");
          sessionStorage.removeItem("auth-user");
          sessionStorage.removeItem("keep-logged-in");
        } else {
          // Store in sessionStorage for session-only login
          sessionStorage.setItem("auth-token", token);
          sessionStorage.setItem("auth-user", JSON.stringify(user));
          // Remove from localStorage if exists
          localStorage.removeItem("auth-token");
          localStorage.removeItem("auth-user");
          localStorage.removeItem("keep-logged-in");
        }

        toast({
          title: "Verification successful! ✅",
          description: `Welcome to Dr. Kumar Laboratories, ${
            user?.name || userName
          }!`,
        });

        // Role-based redirection
        const finalUserRole = user?.role || userRole;
        console.log("User role for redirection:", finalUserRole);
        console.log("User data:", user);

        // Get the appropriate redirect path based on user role
        const redirectPath = getRoleBasedRedirectPath(finalUserRole);
        console.log("Redirecting to:", redirectPath);

        // Navigate to the role-appropriate dashboard
        navigate(redirectPath);
      } else {
        toast({
          title: "Invalid code",
          description:
            response.message || "Please check your code and try again",
          variant: "destructive",
        });
      }
    } catch (error: any) {
      console.error("Two-factor verification error:", error);
      toast({
        title: "Verification failed",
        description: error.message || "An error occurred during verification",
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };

  const handleResendCode = () => {
    toast({
      title: "Code resent",
      description: "A new verification code has been sent to your email",
    });
  };

  return (
    <div className="min-h-screen flex flex-col lg:flex-row">
      {/* Left Side - Form */}
      <div className="flex-1 flex items-center justify-center p-4 sm:p-6 lg:p-8 bg-gray-50">
        <div className="w-full max-w-md space-y-6">
          {/* Logo and Brand */}
          <div className="text-center mb-8">
            <div className="flex justify-center mb-4">
              <img
                src="/lovable-uploads/079f933a-4d0a-4e30-bf46-582b2b7fd97b.png"
                alt="Dr. Kumar Laboratories"
                className="w-20 h-20 object-contain"
              />
            </div>
            <h1 className="text-2xl font-bold text-black">
              Dr. Kumar Laboratories
            </h1>
          </div>

          <div className="space-y-2 text-center sm:text-left">
            <h2 className="text-2xl sm:text-3xl font-bold text-gray-900">
              Two Step Verification
            </h2>
            <p className="text-gray-600 text-sm sm:text-base">
              A verification code has been sent to your email. Please enter it
              in the field below.
            </p>
          </div>

          <form onSubmit={handleVerification} className="space-y-6">
            <div className="space-y-4">
              <p className="text-sm text-gray-700 font-medium">
                Type your 6 digits security code
              </p>

              <div className="flex justify-center">
                <InputOTP
                  maxLength={6}
                  value={otp}
                  onChange={(value) => setOtp(value)}
                  className="gap-2 sm:gap-3"
                >
                  <InputOTPGroup className="gap-2 sm:gap-3">
                    <InputOTPSlot
                      index={0}
                      className="bg-white text-black border-gray-300 w-10 h-10 sm:w-12 sm:h-12 text-lg sm:text-xl"
                    />
                    <InputOTPSlot
                      index={1}
                      className="bg-white text-black border-gray-300 w-10 h-10 sm:w-12 sm:h-12 text-lg sm:text-xl"
                    />
                    <InputOTPSlot
                      index={2}
                      className="bg-white text-black border-gray-300 w-10 h-10 sm:w-12 sm:h-12 text-lg sm:text-xl"
                    />
                    <InputOTPSlot
                      index={3}
                      className="bg-white text-black border-gray-300 w-10 h-10 sm:w-12 sm:h-12 text-lg sm:text-xl"
                    />
                    <InputOTPSlot
                      index={4}
                      className="bg-white text-black border-gray-300 w-10 h-10 sm:w-12 sm:h-12 text-lg sm:text-xl"
                    />
                    <InputOTPSlot
                      index={5}
                      className="bg-white text-black border-gray-300 w-10 h-10 sm:w-12 sm:h-12 text-lg sm:text-xl"
                    />
                  </InputOTPGroup>
                </InputOTP>
              </div>
            </div>

            <Button
              type="submit"
              className="w-full h-12 bg-[#FFD700] hover:bg-[#E6C200] text-black"
              disabled={isLoading}
            >
              {isLoading ? "Verifying..." : "Verify My Account"}
            </Button>
          </form>

          <p className="text-center text-sm text-black">
            Didn't get the code?{" "}
            <button
              onClick={handleResendCode}
              className="text-[#FFD700] hover:underline font-medium"
            >
              Resend
            </button>
          </p>
        </div>
      </div>

      {/* Right Side - Background Image */}
      <div className="hidden lg:block lg:w-1/2 relative">
        <img
          src="/lovable-uploads/********-1ce5-4e40-ad4e-f2133eca9b94.png"
          alt="Doctor Consultation"
          className="w-full h-full object-cover"
        />
        <div className="absolute inset-0 bg-black/20"></div>
      </div>
    </div>
  );
};

export default TwoFactorVerification;
