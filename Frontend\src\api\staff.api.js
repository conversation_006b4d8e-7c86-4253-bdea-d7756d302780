// Staff API Service
import serviceWorkerAPI from "./serviceworker.api.js";

class StaffAPI {
  // Get all staff members with optional filters
  async getStaff(filters = {}) {
    try {
      const response = await serviceWorkerAPI.getStaff(filters);
      console.log("Staff fetched successfully:", response);
      return response;
    } catch (error) {
      console.error("Failed to fetch staff:", error);
      throw error;
    }
  }

  // Get staff member by ID
  async getStaffById(id) {
    try {
      const response = await serviceWorkerAPI.getStaffById(id);
      console.log("Staff member fetched successfully:", response);
      return response;
    } catch (error) {
      console.error("Failed to fetch staff member:", error);
      throw error;
    }
  }

  // Create new staff member
  async createStaff(staffData) {
    try {
      const response = await serviceWorkerAPI.createStaff(staffData);
      console.log("Staff member created successfully:", response);
      return response;
    } catch (error) {
      console.error("Failed to create staff member:", error);
      throw error;
    }
  }

  // Update existing staff member
  async updateStaff(id, staffData) {
    try {
      const response = await serviceWorkerAPI.updateStaff(id, staffData);
      console.log("Staff member updated successfully:", response);
      return response;
    } catch (error) {
      console.error("Failed to update staff member:", error);
      throw error;
    }
  }

  // Delete staff member
  async deleteStaff(id) {
    try {
      const response = await serviceWorkerAPI.deleteStaff(id);
      console.log("Staff member deleted successfully:", response);
      return response;
    } catch (error) {
      console.error("Failed to delete staff member:", error);
      throw error;
    }
  }

  // Update staff member status
  async updateStaffStatus(id, status) {
    try {
      const response = await serviceWorkerAPI.updateStaffStatus(id, status);
      console.log("Staff status updated successfully:", response);
      return response;
    } catch (error) {
      console.error("Failed to update staff status:", error);
      throw error;
    }
  }

  // Search staff members
  async searchStaff(query, filters = {}) {
    try {
      const searchFilters = {
        search: query,
        ...filters,
      };

      const response = await this.getStaff(searchFilters);
      console.log("Staff search completed:", response);
      return response;
    } catch (error) {
      console.error("Failed to search staff:", error);
      throw error;
    }
  }

  // Get staff by status
  async getStaffByStatus(status, filters = {}) {
    try {
      const statusFilters = {
        status,
        ...filters,
      };

      const response = await this.getStaff(statusFilters);
      console.log(`Staff with status '${status}' fetched:`, response);
      return response;
    } catch (error) {
      console.error(`Failed to fetch staff with status '${status}':`, error);
      throw error;
    }
  }

  // Get active staff
  async getActiveStaff(filters = {}) {
    return this.getStaffByStatus("active", filters);
  }

  // Get inactive staff
  async getInactiveStaff(filters = {}) {
    return this.getStaffByStatus("inactive", filters);
  }

  // Get staff by department
  async getStaffByDepartment(department, filters = {}) {
    try {
      const departmentFilters = {
        department,
        ...filters,
      };

      const response = await this.getStaff(departmentFilters);
      console.log(`Staff in department '${department}' fetched:`, response);
      return response;
    } catch (error) {
      console.error(
        `Failed to fetch staff in department '${department}':`,
        error
      );
      throw error;
    }
  }

  // Get staff by role
  async getStaffByRole(role, filters = {}) {
    try {
      const roleFilters = {
        role,
        ...filters,
      };

      const response = await this.getStaff(roleFilters);
      console.log(`Staff with role '${role}' fetched:`, response);
      return response;
    } catch (error) {
      console.error(`Failed to fetch staff with role '${role}':`, error);
      throw error;
    }
  }

  // Get admin staff
  async getAdminStaff(filters = {}) {
    return this.getStaffByRole("admin", filters);
  }

  // Get employee staff
  async getEmployeeStaff(filters = {}) {
    return this.getStaffByRole("employee", filters);
  }

  // Activate staff member
  async activateStaff(id) {
    try {
      const response = await this.updateStaffStatus(id, "active");
      console.log("Staff member activated:", response);
      return response;
    } catch (error) {
      console.error("Failed to activate staff member:", error);
      throw error;
    }
  }

  // Deactivate staff member
  async deactivateStaff(id) {
    try {
      const response = await this.updateStaffStatus(id, "inactive");
      console.log("Staff member deactivated:", response);
      return response;
    } catch (error) {
      console.error("Failed to deactivate staff member:", error);
      throw error;
    }
  }

  // Ban staff member
  async banStaff(id) {
    try {
      const response = await this.updateStaffStatus(id, "banned");
      console.log("Staff member banned:", response);
      return response;
    } catch (error) {
      console.error("Failed to ban staff member:", error);
      throw error;
    }
  }

  // Get staff statistics
  async getStaffStats() {
    try {
      const response = await this.getStaff({ limit: 1 }); // Get minimal data to get stats

      if (response.success && response.data && response.data.stats) {
        console.log("Staff stats fetched:", response.data.stats);
        return {
          success: true,
          data: response.data.stats,
        };
      }

      return response;
    } catch (error) {
      console.error("Failed to fetch staff stats:", error);
      throw error;
    }
  }

  // Bulk update staff status
  async bulkUpdateStaffStatus(staffIds, status) {
    try {
      const promises = staffIds.map((id) => this.updateStaffStatus(id, status));

      const responses = await Promise.allSettled(promises);

      const successful = responses.filter(
        (r) => r.status === "fulfilled"
      ).length;
      const failed = responses.filter((r) => r.status === "rejected").length;

      console.log(
        `Bulk staff status update completed: ${successful} successful, ${failed} failed`
      );

      return {
        success: true,
        message: `Bulk status update completed: ${successful} successful, ${failed} failed`,
        results: responses,
      };
    } catch (error) {
      console.error("Failed to bulk update staff status:", error);
      throw error;
    }
  }

  // Invite staff member (creates staff and sends invitation email)
  async inviteStaff(staffData) {
    try {
      // Validate required fields for invitation
      const requiredFields = ["name", "email", "role", "department"];
      const missingFields = requiredFields.filter((field) => !staffData[field]);

      if (missingFields.length > 0) {
        throw new Error(`Missing required fields: ${missingFields.join(", ")}`);
      }

      // Validate email format
      const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
      if (!emailRegex.test(staffData.email)) {
        throw new Error("Invalid email format");
      }

      // Validate role
      if (!["admin", "employee"].includes(staffData.role)) {
        throw new Error("Role must be either admin or employee");
      }

      // Prepare invitation data (no password needed for invitation)
      const invitationData = {
        name: staffData.name,
        email: staffData.email,
        phone: staffData.phone || "",
        role: staffData.role,
        department: staffData.department,
        position: staffData.position || staffData.role,
        status: staffData.status || "active",
        joinDate: staffData.joinDate || new Date().toISOString().split("T")[0],
      };

      // Create staff member (backend will send invitation email)
      const response = await this.createStaff(invitationData);

      if (response.success) {
        console.log("Staff invitation sent successfully:", response);
        return {
          success: true,
          message: "Staff member invited successfully. Invitation email sent.",
          data: response.data,
        };
      } else {
        throw new Error(response.message || "Failed to send staff invitation");
      }
    } catch (error) {
      console.error("Failed to invite staff member:", error);
      throw error;
    }
  }

  // Create staff with validation (for direct creation with password)
  async createStaffWithValidation(staffData) {
    try {
      // Validate required fields
      const requiredFields = [
        "name",
        "email",
        "password",
        "phone",
        "role",
        "department",
      ];
      const missingFields = requiredFields.filter((field) => !staffData[field]);

      if (missingFields.length > 0) {
        throw new Error(`Missing required fields: ${missingFields.join(", ")}`);
      }

      // Validate email format
      const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
      if (!emailRegex.test(staffData.email)) {
        throw new Error("Invalid email format");
      }

      // Validate role
      if (!["admin", "employee"].includes(staffData.role)) {
        throw new Error("Role must be either admin or employee");
      }

      // Create staff member
      const response = await this.createStaff(staffData);
      return response;
    } catch (error) {
      console.error("Failed to create staff with validation:", error);
      throw error;
    }
  }

  // Get staff summary for dashboard
  async getStaffSummary() {
    try {
      const [allStaff, activeStaff, adminStaff, employeeStaff] =
        await Promise.all([
          this.getStaff({ limit: 1 }),
          this.getActiveStaff({ limit: 1 }),
          this.getAdminStaff({ limit: 1 }),
          this.getEmployeeStaff({ limit: 1 }),
        ]);

      return {
        success: true,
        data: {
          total: allStaff.data?.stats?.total || 0,
          active: activeStaff.data?.stats?.active || 0,
          inactive: allStaff.data?.stats?.inactive || 0,
          admins: adminStaff.data?.pagination?.totalItems || 0,
          employees: employeeStaff.data?.pagination?.totalItems || 0,
          departments: allStaff.data?.stats?.departmentCount || 0,
        },
      };
    } catch (error) {
      console.error("Failed to get staff summary:", error);
      throw error;
    }
  }
}

// Create and export singleton instance
const staffAPI = new StaffAPI();
export default staffAPI;

// Also export the class
export { StaffAPI };
