const express = require('express');
const router = express.Router();
const {
    getAllComplaints,
    updateComplaintStatus,
    updateComplaintPriority,
    getComplaintStats,
    getUrgentComplaints,
    bulkUpdateComplaints
} = require('../../controllers/complaintController');
const { protect, authorize } = require('../../middleware/auth');

// All routes are protected and require admin/employee access
router.use(protect);
router.use(authorize('admin', 'employee'));

// @route   GET /api/admin/complaints
// @desc    Get all complaints (admin)
// @access  Private/Admin
router.get('/', require('../../controllers/complaintController').getAllComplaints);

// @route   PUT /api/admin/complaints/:id/status
// @desc    Update complaint status (admin)
// @access  Private/Admin
router.put('/:id/status', require('../../controllers/complaintController').updateComplaintStatus);

// @route   PUT /api/admin/complaints/:id/priority
// @desc    Update complaint priority (admin)
// @access  Private/Admin
router.put('/:id/priority', require('../../controllers/complaintController').updateComplaintPriority);

// @route   GET /api/admin/complaints/stats
// @desc    Get complaint statistics (admin)
// @access  Private/Admin
router.get('/stats', require('../../controllers/complaintController').getComplaintStats);

// @route   GET /api/admin/complaints/urgent
// @desc    Get urgent complaints (admin)
// @access  Private/Admin
router.get('/urgent', require('../../controllers/complaintController').getUrgentComplaints);

// @route   PUT /api/admin/complaints/bulk-update
// @desc    Bulk update complaints (admin)
// @access  Private/Admin
router.put('/bulk-update', require('../../controllers/complaintController').bulkUpdateComplaints);

module.exports = router;
