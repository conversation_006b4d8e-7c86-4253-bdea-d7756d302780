import {
  Home,
  Package,
  ShoppingCart,
  Users,
  Archive,
  BarChart3,
  Ticket,
  RotateCcw,
  Users2,
  MessageSquare,
  Calendar,
  Stethoscope,
  Star,
} from "lucide-react";
import { Link, useLocation } from "react-router-dom";
import { useState, useEffect } from "react";
import {
  Sidebar,
  SidebarContent,
  SidebarGroup,
  SidebarGroupContent,
  SidebarGroupLabel,
  SidebarMenu,
  SidebarMenuButton,
  SidebarMenuItem,
  SidebarHeader,
} from "@/components/ui/sidebar";
import { authService } from "@/services/authService";

const menuItems = [
  {
    title: "Dashboard",
    url: "/dashboard",
    icon: Home,
    roles: ["admin", "employee"], // Both admins and employees can access dashboard
  },
  {
    title: "Products",
    url: "/products",
    icon: Package,
    roles: ["admin", "employee"], // Both can view products, but employees have restricted actions
    restrictedActions: ["add", "edit"], // Actions restricted for employees
  },
  {
    title: "Orders",
    url: "/orders",
    icon: ShoppingCart,
    roles: ["admin", "employee"], // Both can access orders with full functionality
  },
  {
    title: "Customers",
    url: "/customers",
    icon: Users,
    roles: ["admin", "employee"], // Both can access customers with full functionality
  },
  {
    title: "Inventory",
    url: "/inventory",
    icon: Archive,
    roles: ["admin", "employee"], // Both can view inventory, but employees have restricted actions
    restrictedActions: ["add", "edit"], // Actions restricted for employees
  },
  {
    title: "Consultant",
    url: "/consultant",
    icon: Stethoscope,
    roles: ["admin", "employee"], // Both admins and employees can access consultant with full functionality
  },
  {
    title: "Coupons",
    url: "/coupons",
    icon: Ticket,
    roles: ["admin", "employee"], // Both can view coupons, but employees have restricted actions
    restrictedActions: ["add", "edit"], // Actions restricted for employees
  },
];

const businessTools = [
  {
    title: "Analytics",
    url: "/analytics",
    icon: BarChart3,
    roles: ["admin", "employee"], // Both can access analytics
  },
  {
    title: "Calendar",
    url: "/calendar",
    icon: Calendar,
    roles: ["admin", "employee"], // Both can access calendar
  },
  {
    title: "Feedback",
    url: "/feedback",
    icon: MessageSquare,
    roles: ["admin", "employee"], // Both can access feedback
  },
  {
    title: "Reviews",
    url: "/reviews",
    icon: Star,
    roles: ["admin", "employee"], // Both can access reviews
  },
  {
    title: "Returns",
    url: "/returns",
    icon: RotateCcw,
    roles: ["admin", "employee"], // Both can access returns
  },
];

const otherItems = [
  {
    title: "Staff",
    url: "/staff",
    icon: Users2,
    roles: ["admin", "employee"], // Both can view staff, but employees have view-only access
    restrictedActions: ["add", "edit", "delete"], // All management actions restricted for employees
  },
];

export function AppSidebar() {
  const location = useLocation();
  const [isDarkMode, setIsDarkMode] = useState(false);

  // Get current user role
  const user = authService.getUser();
  const userRole = user?.role || "employee";

  // Filter menu items based on user role
  const filterMenuItems = (items: any[]) => {
    return items.filter((item) => {
      if (!item.roles) return true; // If no roles specified, show to everyone
      return item.roles.includes(userRole);
    });
  };

  const filteredMenuItems = filterMenuItems(menuItems);
  const filteredBusinessTools = filterMenuItems(businessTools);
  const filteredOtherItems = filterMenuItems(otherItems);

  // Check theme on component mount and listen for theme changes
  useEffect(() => {
    const checkTheme = () => {
      const isDark = document.documentElement.classList.contains("dark");
      setIsDarkMode(isDark);
    };

    checkTheme();

    // Create a MutationObserver to watch for theme changes
    const observer = new MutationObserver(checkTheme);
    observer.observe(document.documentElement, {
      attributes: true,
      attributeFilter: ["class"],
    });

    return () => observer.disconnect();
  }, []);

  return (
    <Sidebar className="border-r border-gray-200 dark:border-gray-700 bg-white dark:bg-gray-800 transition-colors duration-500 ease-in-out">
      <SidebarHeader className="p-6 border-b border-gray-200 dark:border-gray-700 transition-colors duration-500 ease-in-out">
        <Link
          to="/dashboard"
          className="flex items-center gap-4 hover:opacity-80 transition-opacity duration-200"
        >
          <div className="relative w-14 h-14 rounded-lg overflow-hidden transition-opacity duration-500 ease-in-out">
            {/* Light mode logo */}
            <img
              src="/lovable-uploads/73a3c9d3-4dc2-424c-af2e-8e092b3e5101.png"
              alt="Dr. Kumar Laboratories"
              className={`absolute inset-0 w-full h-full object-contain transition-opacity duration-500 ease-in-out ${
                isDarkMode ? "opacity-0" : "opacity-100"
              }`}
            />
            {/* Dark mode logo */}
            <img
              src="/lovable-uploads/0df92a85-211b-467d-99a6-583490e9ebba.png"
              alt="Dr. Kumar Laboratories"
              className={`absolute inset-0 w-full h-full object-contain transition-opacity duration-500 ease-in-out ${
                isDarkMode ? "opacity-100" : "opacity-0"
              }`}
            />
          </div>
          <div>
            <h1 className="font-bold text-lg text-gray-900 dark:text-white transition-colors duration-500 ease-in-out">
              Dr. Kumar Laboratories
            </h1>
            <p className="text-base text-gray-500 dark:text-gray-400 transition-colors duration-500 ease-in-out">
              Admin Dashboard
            </p>
          </div>
        </Link>
      </SidebarHeader>

      <SidebarContent className="p-4">
        <SidebarGroup>
          <SidebarGroupLabel className="text-xs font-medium text-gray-500 dark:text-gray-400 mb-2 transition-colors duration-500 ease-in-out">
            Main Menu
          </SidebarGroupLabel>
          <SidebarGroupContent>
            <SidebarMenu>
              {filteredMenuItems.map((item) => (
                <SidebarMenuItem key={item.title}>
                  <SidebarMenuButton
                    asChild
                    className={`${
                      location.pathname === item.url
                        ? "bg-green-100 dark:bg-green-900/30 text-green-700 dark:text-green-400 border-l-4 border-green-500"
                        : "text-black dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-green-800/20 dark:hover:text-green-300"
                    } transition-colors duration-300 ease-in-out text-base`}
                  >
                    <Link
                      to={item.url}
                      className="flex items-center justify-between"
                    >
                      <div className="flex items-center gap-3">
                        <item.icon className="w-5 h-5" />
                        <span className="font-medium">{item.title}</span>
                      </div>
                    </Link>
                  </SidebarMenuButton>
                </SidebarMenuItem>
              ))}
            </SidebarMenu>
          </SidebarGroupContent>
        </SidebarGroup>

        <SidebarGroup>
          <SidebarGroupLabel className="text-xs font-medium text-gray-500 dark:text-gray-400 mb-2 transition-colors duration-500 ease-in-out">
            Business Tools
          </SidebarGroupLabel>
          <SidebarGroupContent>
            <SidebarMenu>
              {filteredBusinessTools.map((item) => (
                <SidebarMenuItem key={item.title}>
                  <SidebarMenuButton
                    asChild
                    className={`${
                      location.pathname === item.url
                        ? "bg-green-100 dark:bg-green-900/30 text-green-700 dark:text-green-400 border-l-4 border-green-500"
                        : "text-black dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-green-800/20 dark:hover:text-green-300"
                    } transition-colors duration-300 ease-in-out text-base`}
                  >
                    <Link to={item.url} className="flex items-center gap-3">
                      <item.icon className="w-5 h-5" />
                      <span className="font-medium">{item.title}</span>
                    </Link>
                  </SidebarMenuButton>
                </SidebarMenuItem>
              ))}
            </SidebarMenu>
          </SidebarGroupContent>
        </SidebarGroup>

        <SidebarGroup>
          <SidebarGroupContent>
            <SidebarMenu>
              {filteredOtherItems.map((item) => (
                <SidebarMenuItem key={item.title}>
                  <SidebarMenuButton
                    asChild
                    className={`${
                      location.pathname === item.url
                        ? "bg-green-100 dark:bg-green-900/30 text-green-700 dark:text-green-400 border-l-4 border-green-500"
                        : "text-black dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-green-800/20 dark:hover:text-green-300"
                    } transition-colors duration-300 ease-in-out text-base`}
                  >
                    <Link to={item.url} className="flex items-center gap-3">
                      <item.icon className="w-5 h-5" />
                      <span className="font-medium">{item.title}</span>
                    </Link>
                  </SidebarMenuButton>
                </SidebarMenuItem>
              ))}
            </SidebarMenu>
          </SidebarGroupContent>
        </SidebarGroup>
      </SidebarContent>
    </Sidebar>
  );
}
