const express = require('express');
const router = express.Router();
const {
  getAllStaff,
  getStaffById,
  createStaff,
  updateStaff,
  deleteStaff,
  updateStaffStatus
} = require('../../controllers/staffController');
const { protect, adminOnly } = require('../../middleware/authMiddleware');

// Apply authentication middleware to all routes
router.use(protect);
router.use(adminOnly); // Only admins can access staff management

// @route   GET /api/admin/staff
// @desc    Get all staff members with pagination and filters
// @access  Private (Admin only)
router.get('/', getAllStaff);

// @route   GET /api/admin/staff/:id
// @desc    Get staff member by ID
// @access  Private (Admin only)
router.get('/:id', getStaffById);

// @route   POST /api/admin/staff
// @desc    Create new staff member
// @access  Private (Admin only)
router.post('/', createStaff);

// @route   PUT /api/admin/staff/:id
// @desc    Update staff member
// @access  Private (Admin only)
router.put('/:id', updateStaff);

// @route   DELETE /api/admin/staff/:id
// @desc    Delete staff member
// @access  Private (Admin only)
router.delete('/:id', deleteStaff);

// @route   PUT /api/admin/staff/:id/status
// @desc    Update staff member status
// @access  Private (Admin only)
router.put('/:id/status', updateStaffStatus);

module.exports = router;
