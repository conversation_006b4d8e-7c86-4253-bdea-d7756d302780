const express = require("express");
const router = express.Router();
const {
  getAllStaff,
  getStaffById,
  createStaff,
  updateStaff,
  deleteStaff,
  updateStaffStatus,
} = require("../../controllers/staffController");
const {
  protect,
  adminOnly,
  employeeOrAdmin,
} = require("../../middleware/authMiddleware");

// Apply authentication middleware to all routes
router.use(protect);

// Routes accessible by both admin and employee (view-only for employees)
// @route   GET /api/admin/staff
// @desc    Get all staff members with pagination and filters
// @access  Private (Admin/Employee)
router.get("/", employeeOrAdmin, getAllStaff);

// @route   GET /api/admin/staff/:id
// @desc    Get staff member by ID
// @access  Private (Admin/Employee)
router.get("/:id", employeeOrAdmin, getStaffById);

// Admin-only routes (create, update, delete)
// @route   POST /api/admin/staff
// @desc    Create new staff member
// @access  Private (Admin only)
router.post("/", adminOnly, createStaff);

// @route   PUT /api/admin/staff/:id
// @desc    Update staff member
// @access  Private (Admin only)
router.put("/:id", adminOnly, updateStaff);

// @route   DELETE /api/admin/staff/:id
// @desc    Delete staff member
// @access  Private (Admin only)
router.delete("/:id", adminOnly, deleteStaff);

// @route   PUT /api/admin/staff/:id/status
// @desc    Update staff member status
// @access  Private (Admin only)
router.put("/:id/status", updateStaffStatus);

module.exports = router;
