const express = require("express");
const router = express.Router();

// Import controller functions
const {
  getCategories,
  getCategoryById,
  createCategory,
  updateCategory,
  deleteCategory,
  getCategoryStats,
  getCategoriesWithProductCount,
  getProductsByCategory,
  bulkDeleteCategories,
} = require("../../controllers/categoryController");

// Import authentication middleware
const { protect, authorize } = require("../../middleware/auth");

// All routes are protected and require admin access
router.use(protect);
router.use(authorize("admin"));

// Specific routes first (before parameterized routes)
// @route   GET /api/admin/categories/productcount
// @desc    Get categories with product count (admin)
// @access  Private/Admin
router.get("/productcount", getCategoriesWithProductCount);

// @route   GET /api/admin/categories/category-products
// @desc    Get products by category (admin)
// @access  Private/Admin
router.get("/category-products", getProductsByCategory);

// @route   GET /api/admin/categories/stats
// @desc    Get category statistics (admin)
// @access  Private/Admin
router.get("/stats", getCategoryStats);

// @route   DELETE /api/admin/categories/bulk-delete
// @desc    Bulk delete categories (admin)
// @access  Private/Admin
router.delete("/bulk-delete", bulkDeleteCategories);

// General routes
// @route   GET /api/admin/categories
// @desc    Get all categories (admin)
// @access  Private/Admin
router.get("/", getCategories);

// @route   POST /api/admin/categories
// @desc    Create category (admin)
// @access  Private/Admin
router.post("/", createCategory);

// Parameterized routes last
// @route   GET /api/admin/categories/:id
// @desc    Get category by ID (admin)
// @access  Private/Admin
router.get("/:id", getCategoryById);

// @route   PUT /api/admin/categories/:id
// @desc    Update category (admin)
// @access  Private/Admin
router.put("/:id", updateCategory);

// @route   DELETE /api/admin/categories/:id
// @desc    Delete category (admin)
// @access  Private/Admin
router.delete("/:id", deleteCategory);

module.exports = router;
