const express = require("express");
const router = express.Router();

// Import controller functions
const {
  getAllOrders,
  getOrderById,
  updateOrderStatus,
  deleteOrder,
  getOrderStats,
} = require("../../controllers/orderController");

// Import authentication middleware
const { protect, authorize } = require("../../middleware/auth");

// All routes are protected and require admin access
router.use(protect);
router.use(authorize("admin"));

// Specific routes first
// @route   GET /api/admin/orders/stats
// @desc    Get order statistics
// @access  Private/Admin
router.get("/stats", getOrderStats);

// General routes
// @route   GET /api/admin/orders
// @desc    Get all orders (admin)
// @access  Private/Admin
router.get("/", getAllOrders);

// Parameterized routes last
// @route   GET /api/admin/orders/:id
// @desc    Get order by ID (admin)
// @access  Private/Admin
router.get("/:id", getOrderById);

// @route   PUT /api/admin/orders/:id/status
// @desc    Update order status
// @access  Private/Admin
router.put("/:id/status", updateOrderStatus);

// @route   DELETE /api/admin/orders/:id
// @desc    Delete order (admin)
// @access  Private/Admin
router.delete("/:id", deleteOrder);

module.exports = router;
