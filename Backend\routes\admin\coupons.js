const express = require('express');
const router = express.Router();
const {
    getAllCoupons,
    getCouponById,
    createCoupon,
    updateCoupon,
    deleteCoupon,
    toggleCouponStatus
} = require('../../controllers/couponController');
const { protect, authorize } = require('../../middleware/auth');

// All routes are protected and require admin access
router.use(protect);
router.use(authorize('admin', 'employee'));

// @route   GET /api/admin/coupons
// @desc    Get all coupons (admin)
// @access  Private/Admin
router.get('/', getAllCoupons);

// @route   GET /api/admin/coupons/:id
// @desc    Get coupon by ID (admin)
// @access  Private/Admin
router.get('/:id', getCouponById);

// @route   POST /api/admin/coupons
// @desc    Create new coupon (admin)
// @access  Private/Admin
router.post('/', createCoupon);

// @route   PUT /api/admin/coupons/:id
// @desc    Update coupon (admin)
// @access  Private/Admin
router.put('/:id', updateCoupon);

// @route   DELETE /api/admin/coupons/:id
// @desc    Delete coupon (admin)
// @access  Private/Admin
router.delete('/:id', deleteCoupon);

// @route   PUT /api/admin/coupons/:id/status
// @desc    Toggle coupon status (admin)
// @access  Private/Admin
router.put('/:id/status', toggleCouponStatus);

module.exports = router;
