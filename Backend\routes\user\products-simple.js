const express = require("express");
const router = express.Router();

// Import controller functions
const {
  getProducts,
  getProductById,
  getFeaturedProducts,
} = require("../../controllers/productController");

// @route   GET /api/user/products/featured
// @desc    Get featured products
// @access  Public
router.get("/featured", getFeaturedProducts);

// @route   GET /api/user/products
// @desc    Get all products (public)
// @access  Public
router.get("/", getProducts);

// @route   GET /api/user/products/:id
// @desc    Get product by ID (public)
// @access  Public
router.get("/:id", getProductById);

module.exports = router;
