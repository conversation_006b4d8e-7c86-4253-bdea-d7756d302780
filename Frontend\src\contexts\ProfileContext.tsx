import React, { createContext, useContext, useState, useEffect } from "react";
import authAPI from "@/api/auth.api.js";
import serviceWorkerAPI from "@/api/serviceworker.api.js";
import { apiHelpers } from "@/utils/apiHelpers";

interface ProfileData {
  name: string;
  email: string;
  phone: string;
  jobTitle: string;
  department: string;
  profileImage: string;
  role?: string;
  isActive?: boolean;
}

interface ProfileContextType {
  profileData: ProfileData;
  updateProfileData: (data: Partial<ProfileData>) => void;
  updateProfileImage: (imageUrl: string) => void;
  getInitials: (name: string) => string;
  fetchProfileData: () => Promise<void>;
  loading: boolean;
}

const ProfileContext = createContext<ProfileContextType | undefined>(undefined);

const PROFILE_STORAGE_KEY = "dr-kumar-profile-data";

export const ProfileProvider: React.FC<{ children: React.ReactNode }> = ({
  children,
}) => {
  const [loading, setLoading] = useState(true);
  const [profileData, setProfileData] = useState<ProfileData>(() => {
    // Try to load from localStorage first as fallback
    const stored = localStorage.getItem(PROFILE_STORAGE_KEY);
    if (stored) {
      try {
        return JSON.parse(stored);
      } catch (error) {
        console.error("Error parsing stored profile data:", error);
      }
    }

    // Default profile data (fallback)
    return {
      name: "Loading...",
      email: "<EMAIL>",
      phone: "+91 **********",
      jobTitle: "Loading...",
      department: "Loading...",
      profileImage: "",
    };
  });

  // Fetch profile data from backend
  const fetchProfileData = async () => {
    try {
      setLoading(true);

      // First try to get current user from auth
      const currentUser = authAPI.getCurrentUser();
      if (!currentUser) {
        console.log("No authenticated user found");
        return;
      }

      // Fetch full profile data from backend
      const response = await serviceWorkerAPI.getUserProfile();

      if (apiHelpers.isSuccessResponse(response)) {
        const userData = apiHelpers.extractData(response);
        const user = userData.user || userData;

        const formattedProfileData: ProfileData = {
          name: user.name || currentUser.name || "Unknown User",
          email: user.email || currentUser.email || "<EMAIL>",
          phone: user.phone || "+91 **********",
          jobTitle:
            user.position ||
            user.jobTitle ||
            (user.role === "admin" ? "Administrator" : "Employee"),
          department: user.department || "General",
          profileImage: user.profileImage?.url || user.profileImage || "",
          role: user.role || currentUser.role,
          isActive: user.isActive !== undefined ? user.isActive : true,
        };

        setProfileData(formattedProfileData);
        localStorage.setItem(
          PROFILE_STORAGE_KEY,
          JSON.stringify(formattedProfileData)
        );
        console.log("Profile data loaded from backend:", formattedProfileData);
      } else {
        console.error("Failed to fetch profile data:", response.message);
        // Use current user data as fallback
        if (currentUser) {
          const fallbackData: ProfileData = {
            name: currentUser.name || "Unknown User",
            email: currentUser.email || "<EMAIL>",
            phone: "+91 **********",
            jobTitle:
              currentUser.role === "admin" ? "Administrator" : "Employee",
            department: "General",
            profileImage: "",
            role: currentUser.role,
            isActive: true,
          };
          setProfileData(fallbackData);
        }
      }
    } catch (error) {
      console.error("Error fetching profile data:", error);
      // Use current user data as fallback
      const currentUser = authAPI.getCurrentUser();
      if (currentUser) {
        const fallbackData: ProfileData = {
          name: currentUser.name || "Unknown User",
          email: currentUser.email || "<EMAIL>",
          phone: "+91 **********",
          jobTitle: currentUser.role === "admin" ? "Administrator" : "Employee",
          department: "General",
          profileImage: "",
          role: currentUser.role,
          isActive: true,
        };
        setProfileData(fallbackData);
      }
    } finally {
      setLoading(false);
    }
  };

  // Fetch profile data on component mount
  useEffect(() => {
    fetchProfileData();
  }, []);

  // Save to localStorage whenever profileData changes (but not during loading)
  useEffect(() => {
    if (!loading) {
      localStorage.setItem(PROFILE_STORAGE_KEY, JSON.stringify(profileData));
    }
  }, [profileData, loading]);

  const updateProfileData = (data: Partial<ProfileData>) => {
    setProfileData((prev) => ({ ...prev, ...data }));
  };

  const updateProfileImage = (imageUrl: string) => {
    setProfileData((prev) => ({ ...prev, profileImage: imageUrl }));
  };

  const getInitials = (name: string) => {
    return name
      .split(" ")
      .map((n) => n[0])
      .join("")
      .toUpperCase();
  };

  const value: ProfileContextType = {
    profileData,
    updateProfileData,
    updateProfileImage,
    getInitials,
    fetchProfileData,
    loading,
  };

  return (
    <ProfileContext.Provider value={value}>{children}</ProfileContext.Provider>
  );
};

export const useProfile = () => {
  const context = useContext(ProfileContext);
  if (context === undefined) {
    throw new Error("useProfile must be used within a ProfileProvider");
  }
  return context;
};
