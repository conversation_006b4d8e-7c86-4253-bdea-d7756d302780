const Product = require('../models/Products');
const User = require('../models/User');

// @desc    Get all reviews (admin)
// @route   GET /api/admin/reviews
// @access  Private/Admin
const getAllReviews = async (req, res) => {
    try {
        const {
            page = 1,
            limit = 10,
            rating,
            status,
            search,
            sortBy = 'createdAt',
            sortOrder = 'desc'
        } = req.query;

        // Build aggregation pipeline to get all reviews from all products
        const pipeline = [
            // Unwind reviews array to get individual review documents
            { $unwind: '$reviews' },
            
            // Lookup user information
            {
                $lookup: {
                    from: 'users',
                    localField: 'reviews.user',
                    foreignField: '_id',
                    as: 'reviews.userInfo'
                }
            },
            
            // Unwind user info
            { $unwind: '$reviews.userInfo' },
            
            // Project the fields we need
            {
                $project: {
                    _id: '$reviews._id',
                    productId: '$_id',
                    productName: '$name',
                    productImage: { $arrayElemAt: ['$images.url', 0] },
                    user: {
                        _id: '$reviews.userInfo._id',
                        name: '$reviews.userInfo.name',
                        email: '$reviews.userInfo.email'
                    },
                    rating: '$reviews.rating',
                    comment: '$reviews.comment',
                    status: { $ifNull: ['$reviews.status', 'approved'] }, // Default to approved for existing reviews
                    createdAt: { $ifNull: ['$reviews.createdAt', new Date()] },
                    updatedAt: { $ifNull: ['$reviews.updatedAt', new Date()] }
                }
            }
        ];

        // Add filters
        const matchConditions = {};
        
        if (rating) {
            matchConditions.rating = parseInt(rating);
        }
        
        if (status) {
            matchConditions.status = status;
        }
        
        if (search) {
            matchConditions.$or = [
                { productName: { $regex: search, $options: 'i' } },
                { 'user.name': { $regex: search, $options: 'i' } },
                { 'user.email': { $regex: search, $options: 'i' } },
                { comment: { $regex: search, $options: 'i' } }
            ];
        }

        if (Object.keys(matchConditions).length > 0) {
            pipeline.push({ $match: matchConditions });
        }

        // Add sorting
        const sortOptions = {};
        sortOptions[sortBy] = sortOrder === 'desc' ? -1 : 1;
        pipeline.push({ $sort: sortOptions });

        // Execute aggregation
        const reviews = await Product.aggregate(pipeline);

        // Pagination
        const startIndex = (parseInt(page) - 1) * parseInt(limit);
        const endIndex = startIndex + parseInt(limit);
        const paginatedReviews = reviews.slice(startIndex, endIndex);

        // Get total count for pagination
        const totalReviews = reviews.length;
        const totalPages = Math.ceil(totalReviews / parseInt(limit));

        res.json({
            success: true,
            reviews: paginatedReviews,
            pagination: {
                currentPage: parseInt(page),
                totalPages,
                totalReviews,
                hasNextPage: parseInt(page) < totalPages,
                hasPrevPage: parseInt(page) > 1
            }
        });

    } catch (error) {
        console.error('Get all reviews error:', error);
        res.status(500).json({
            success: false,
            message: 'Server error fetching reviews',
            error: error.message
        });
    }
};

// @desc    Get review by ID
// @route   GET /api/admin/reviews/:id
// @access  Private/Admin
const getReviewById = async (req, res) => {
    try {
        const reviewId = req.params.id;

        // Find the product that contains this review
        const product = await Product.findOne({ 'reviews._id': reviewId })
            .populate('reviews.user', 'name email')
            .populate('categoryId', 'name');

        if (!product) {
            return res.status(404).json({
                success: false,
                message: 'Review not found'
            });
        }

        // Find the specific review
        const review = product.reviews.id(reviewId);
        
        if (!review) {
            return res.status(404).json({
                success: false,
                message: 'Review not found'
            });
        }

        // Format the response
        const formattedReview = {
            _id: review._id,
            productId: product._id,
            productName: product.name,
            productImage: product.images[0]?.url || '',
            user: review.user,
            rating: review.rating,
            comment: review.comment,
            status: review.status || 'approved',
            createdAt: review.createdAt,
            updatedAt: review.updatedAt
        };

        res.json({
            success: true,
            review: formattedReview
        });

    } catch (error) {
        console.error('Get review by ID error:', error);
        res.status(500).json({
            success: false,
            message: 'Server error fetching review',
            error: error.message
        });
    }
};

// @desc    Update review status
// @route   PUT /api/admin/reviews/:id/status
// @access  Private/Admin
const updateReviewStatus = async (req, res) => {
    try {
        const reviewId = req.params.id;
        const { status } = req.body;

        if (!['approved', 'rejected', 'pending'].includes(status)) {
            return res.status(400).json({
                success: false,
                message: 'Invalid status. Must be approved, rejected, or pending'
            });
        }

        // Find the product that contains this review
        const product = await Product.findOne({ 'reviews._id': reviewId });

        if (!product) {
            return res.status(404).json({
                success: false,
                message: 'Review not found'
            });
        }

        // Find and update the specific review
        const review = product.reviews.id(reviewId);
        
        if (!review) {
            return res.status(404).json({
                success: false,
                message: 'Review not found'
            });
        }

        review.status = status;
        review.updatedAt = new Date();

        await product.save();

        res.json({
            success: true,
            message: `Review status updated to ${status}`,
            review: {
                _id: review._id,
                status: review.status,
                updatedAt: review.updatedAt
            }
        });

    } catch (error) {
        console.error('Update review status error:', error);
        res.status(500).json({
            success: false,
            message: 'Server error updating review status',
            error: error.message
        });
    }
};

// @desc    Delete review
// @route   DELETE /api/admin/reviews/:id
// @access  Private/Admin
const deleteReview = async (req, res) => {
    try {
        const reviewId = req.params.id;

        // Find the product that contains this review
        const product = await Product.findOne({ 'reviews._id': reviewId });

        if (!product) {
            return res.status(404).json({
                success: false,
                message: 'Review not found'
            });
        }

        // Remove the review
        product.reviews.id(reviewId).remove();
        
        // Update review statistics
        await product.updateReviewStats();

        res.json({
            success: true,
            message: 'Review deleted successfully'
        });

    } catch (error) {
        console.error('Delete review error:', error);
        res.status(500).json({
            success: false,
            message: 'Server error deleting review',
            error: error.message
        });
    }
};

// @desc    Get review statistics
// @route   GET /api/admin/reviews/stats
// @access  Private/Admin
const getReviewStats = async (req, res) => {
    try {
        // Get all reviews using aggregation
        const stats = await Product.aggregate([
            { $unwind: '$reviews' },
            {
                $group: {
                    _id: null,
                    totalReviews: { $sum: 1 },
                    averageRating: { $avg: '$reviews.rating' },
                    approved: {
                        $sum: {
                            $cond: [
                                { $eq: [{ $ifNull: ['$reviews.status', 'approved'] }, 'approved'] },
                                1,
                                0
                            ]
                        }
                    },
                    pending: {
                        $sum: {
                            $cond: [
                                { $eq: ['$reviews.status', 'pending'] },
                                1,
                                0
                            ]
                        }
                    },
                    rejected: {
                        $sum: {
                            $cond: [
                                { $eq: ['$reviews.status', 'rejected'] },
                                1,
                                0
                            ]
                        }
                    },
                    fiveStars: {
                        $sum: {
                            $cond: [{ $eq: ['$reviews.rating', 5] }, 1, 0]
                        }
                    },
                    fourStars: {
                        $sum: {
                            $cond: [{ $eq: ['$reviews.rating', 4] }, 1, 0]
                        }
                    },
                    threeStars: {
                        $sum: {
                            $cond: [{ $eq: ['$reviews.rating', 3] }, 1, 0]
                        }
                    },
                    twoStars: {
                        $sum: {
                            $cond: [{ $eq: ['$reviews.rating', 2] }, 1, 0]
                        }
                    },
                    oneStar: {
                        $sum: {
                            $cond: [{ $eq: ['$reviews.rating', 1] }, 1, 0]
                        }
                    }
                }
            }
        ]);

        const result = stats[0] || {
            totalReviews: 0,
            averageRating: 0,
            approved: 0,
            pending: 0,
            rejected: 0,
            fiveStars: 0,
            fourStars: 0,
            threeStars: 0,
            twoStars: 0,
            oneStar: 0
        };

        res.json({
            success: true,
            stats: result
        });

    } catch (error) {
        console.error('Get review stats error:', error);
        res.status(500).json({
            success: false,
            message: 'Server error fetching review statistics',
            error: error.message
        });
    }
};

module.exports = {
    getAllReviews,
    getReviewById,
    updateReviewStatus,
    deleteReview,
    getReviewStats
};
